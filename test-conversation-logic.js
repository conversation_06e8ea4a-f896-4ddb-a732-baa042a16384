// Test script to demonstrate the conversation logic and assumption changing
import { analyzeConversationState, generateSystemPrompt, getAssumptionGuidance } from './backend/prompts/generators.js';

// Mock conversation scenarios
const scenarios = [
  {
    name: "Initial Request",
    messages: [
      { role: 'user', content: 'I want to build a web app' }
    ]
  },
  {
    name: "After First Question",
    messages: [
      { role: 'user', content: 'I want to build a web app' },
      { role: 'assistant', content: 'Q: What type of users will be using this app?\nAssume: Business professionals in corporate environments' },
      { role: 'user', content: 'Small business owners' }
    ]
  },
  {
    name: "After Second Question", 
    messages: [
      { role: 'user', content: 'I want to build a web app' },
      { role: 'assistant', content: 'Q: What type of users will be using this app?\nAssume: Business professionals in corporate environments' },
      { role: 'user', content: 'Small business owners' },
      { role: 'assistant', content: 'Q: What core features do you need?\nAssume: User management, data processing, and reporting tools' },
      { role: 'user', content: 'Inventory management and sales tracking' }
    ]
  },
  {
    name: "After Third Question (Should trigger end)",
    messages: [
      { role: 'user', content: 'I want to build a web app' },
      { role: 'assistant', content: 'Q: What type of users will be using this app?\nAssume: Business professionals in corporate environments' },
      { role: 'user', content: 'Small business owners' },
      { role: 'assistant', content: 'Q: What core features do you need?\nAssume: User management, data processing, and reporting tools' },
      { role: 'user', content: 'Inventory management and sales tracking' },
      { role: 'assistant', content: 'Q: What technology stack do you prefer?\nAssume: Web-based solution with cloud deployment' },
      { role: 'user', content: 'React and Node.js' }
    ]
  }
];

console.log('=== TESTING CONVERSATION LOGIC ===\n');

scenarios.forEach((scenario, index) => {
  console.log(`${index + 1}. ${scenario.name}`);
  console.log('Messages:', scenario.messages.length);
  
  const state = analyzeConversationState(scenario.messages, false);
  console.log('State:', {
    questionCount: state.questionCount,
    userResponseCount: state.userResponseCount,
    shouldShowEndPrompt: state.shouldShowEndPrompt,
    shouldAutoGenerateQuote: state.shouldAutoGenerateQuote
  });
  
  const guidance = getAssumptionGuidance(scenario.messages, state.questionCount);
  console.log('Assumption Guidance:', guidance);
  
  const systemPrompt = generateSystemPrompt('web', state, scenario.messages);
  console.log('System Prompt Type:', 
    systemPrompt.includes('END_PROMPT_SYSTEM_MESSAGE') ? 'END PROMPT' :
    systemPrompt.includes('CONTINUE_QUESTIONING_PROMPT') ? 'CONTINUE QUESTIONING' :
    systemPrompt.includes('VAGUE_REQUEST_PROMPT') ? 'VAGUE REQUEST' : 'OTHER'
  );
  
  console.log('---\n');
});

console.log('=== TESTING ASSUMPTION VARIATION ===\n');

// Test assumption guidance for different question counts
for (let i = 0; i < 6; i++) {
  const mockMessages = [
    { role: 'user', content: 'I want to build an app' },
    ...Array(i).fill(null).map((_, idx) => [
      { role: 'assistant', content: `Q: Question ${idx + 1}?\nAssume: Previous assumption ${idx + 1}` },
      { role: 'user', content: `Answer ${idx + 1}` }
    ]).flat()
  ];
  
  const guidance = getAssumptionGuidance(mockMessages, i);
  console.log(`Question ${i + 1} Guidance:`, guidance);
}
