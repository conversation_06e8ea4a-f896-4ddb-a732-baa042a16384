{"swagger": "2.0", "info": {"description": "API Gateway for Quote Generator Backend", "version": "2025-08-08T20:57:30Z", "title": "quote-gen-bloodandtreasure-api"}, "host": "5dacn8xif1.execute-api.us-east-1.amazonaws.com", "basePath": "/prod", "schemes": ["https"], "paths": {"/": {"options": {"consumes": ["application/json"], "responses": {"200": {"description": "200 response", "headers": {"Access-Control-Allow-Origin": {"type": "string"}, "Access-Control-Allow-Methods": {"type": "string"}, "Access-Control-Allow-Headers": {"type": "string"}}}}}, "x-amazon-apigateway-any-method": {"responses": {}}}, "/{proxy+}": {"options": {"consumes": ["application/json"], "parameters": [{"name": "proxy", "in": "path", "required": true, "type": "string"}], "responses": {"200": {"description": "200 response", "headers": {"Access-Control-Allow-Origin": {"type": "string"}, "Access-Control-Allow-Methods": {"type": "string"}, "Access-Control-Allow-Headers": {"type": "string"}}}}}, "x-amazon-apigateway-any-method": {"parameters": [{"name": "proxy", "in": "path", "required": true, "type": "string"}], "responses": {}}}}}