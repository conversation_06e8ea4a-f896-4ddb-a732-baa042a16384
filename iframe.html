
<style>
    @keyframes spin { 0% { transform: rotate(0deg); } 100% { transform: rotate(360deg); } }
    @media (max-width: 768px) {
        .qg-launch-button { padding: 16px 32px !important; font-size: 16px !important; }
        .qg-modal-overlay { padding: 10px !important; }
        .qg-modal-content { width: 98vw !important; height: 95vh !important; border-radius: 15px !important; }
        .qg-close-btn { width: 35px !important; height: 35px !important; top: 10px !important; right: 10px !important; }
        .qg-close-btn svg { width: 18px !important; height: 18px !important; }
        h1 { font-size: 24px !important; }
    }
</style>

<div style="font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif; background: black; min-height: 100vh; display: flex; flex-direction: column; align-items: center; justify-content: center; padding: 20px; margin: 0; gap: 30px;">
    <!-- Heading Text -->
    <h1 style="color: white; font-size: 32px; font-weight: 600; text-align: center; margin: 0; line-height: 1.2;">
        What do you want to build today?
    </h1>

    <!-- Launch Button -->
    <button id="qg-launch-btn" class="qg-launch-button" style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; border: none; padding: 20px 40px; font-size: 18px; font-weight: 600; border-radius: 50px; cursor: pointer; box-shadow: 0 8px 32px rgba(102, 126, 234, 0.3); transition: all 0.3s ease; backdrop-filter: blur(10px); border: 1px solid rgba(255, 255, 255, 0.2);" onmouseover="this.style.transform='translateY(-2px)'; this.style.boxShadow='0 12px 40px rgba(102, 126, 234, 0.4)'" onmouseout="this.style.transform='translateY(0)'; this.style.boxShadow='0 8px 32px rgba(102, 126, 234, 0.3)'" onmousedown="this.style.transform='translateY(0)'" onmouseup="this.style.transform='translateY(-2px)'" onclick="openModal()">
        Generate your quote
    </button>

    <script>
        // Get DOM elements
        const launchBtn = document.getElementById('qg-launch-btn');

        // State management
        let isModalOpen = false;
        let iframeLoaded = false;
        let modalOverlay = null;
        let modalContent = null;
        let closeBtn = null;
        let iframe = null;
        let loading = null;

        // Create modal dynamically
        function createModal() {
            console.log('Creating modal...'); // Debug log
            if (modalOverlay) return; // Already created

            // Create modal overlay
            modalOverlay = document.createElement('div');
            modalOverlay.id = 'qg-modal-overlay';
            modalOverlay.className = 'qg-modal-overlay';
            modalOverlay.style.cssText = 'position: fixed; top: 0; left: 0; width: 100vw; height: 100vh; background: rgba(0, 0, 0, 0.8); backdrop-filter: blur(10px); z-index: 9999; display: none; align-items: center; justify-content: center; padding: 20px; opacity: 0; transition: opacity 0.3s ease; font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;';

            // Create modal content
            modalContent = document.createElement('div');
            modalContent.className = 'qg-modal-content';
            modalContent.style.cssText = 'width: 95vw; height: 90vh; max-width: 1200px; max-height: 800px; background: rgba(255, 255, 255, 0.1); backdrop-filter: blur(10px); border-radius: 20px; border: 1px solid rgba(255, 255, 255, 0.2); box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3); position: relative; overflow: hidden; transform: scale(0.9); transition: transform 0.3s ease;';

            // Create iframe
            iframe = document.createElement('iframe');
            iframe.id = 'qg-quote-generator-iframe';
            iframe.src = 'https://quote-gen.bloodandtreasure.com';
            iframe.title = 'Quote Generator';
            iframe.frameBorder = '0';
            iframe.scrolling = 'auto';
            iframe.allowFullscreen = true;
            iframe.style.cssText = 'width: 100%; height: 100%; border: none; border-radius: inherit;';

            // Create loading indicator
            loading = document.createElement('div');
            loading.id = 'qg-loading-indicator';
            loading.style.cssText = 'position: absolute; top: 50%; left: 50%; transform: translate(-50%, -50%); color: rgba(255, 255, 255, 0.8); font-size: 16px; z-index: 5; display: flex; align-items: center; gap: 10px;';

            const spinner = document.createElement('div');
            spinner.className = 'qg-loading-spinner';
            spinner.style.cssText = 'width: 20px; height: 20px; border: 2px solid rgba(255, 255, 255, 0.3); border-top: 2px solid rgba(255, 255, 255, 0.8); border-radius: 50%; animation: spin 1s linear infinite;';

            loading.appendChild(spinner);
            loading.appendChild(document.createTextNode('Loading Quote Generator...'));

            // Create close button
            closeBtn = document.createElement('button');
            closeBtn.id = 'qg-close-btn';
            closeBtn.className = 'qg-close-btn';
            closeBtn.title = 'Close';
            closeBtn.style.cssText = 'position: absolute; top: 15px; right: 15px; width: 40px; height: 40px; background: rgba(255, 255, 255, 0.9); border: none; border-radius: 50%; cursor: pointer; display: flex; align-items: center; justify-content: center; transition: all 0.2s ease; box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2); z-index: 10;';

            closeBtn.innerHTML = '<svg viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" style="width: 20px; height: 20px; color: #333;"><line x1="18" y1="6" x2="6" y2="18"></line><line x1="6" y1="6" x2="18" y2="18"></line></svg>';

            // Add hover effects to close button
            closeBtn.onmouseover = function() {
                this.style.background = 'rgba(255, 255, 255, 1)';
                this.style.transform = 'scale(1.1)';
            };
            closeBtn.onmouseout = function() {
                this.style.background = 'rgba(255, 255, 255, 0.9)';
                this.style.transform = 'scale(1)';
            };
            closeBtn.onmousedown = function() {
                this.style.transform = 'scale(0.95)';
            };
            closeBtn.onmouseup = function() {
                this.style.transform = 'scale(1.1)';
            };

            // Assemble modal
            modalContent.appendChild(iframe);
            modalContent.appendChild(loading);
            modalContent.appendChild(closeBtn);
            modalOverlay.appendChild(modalContent);

            // Append to body
            document.body.appendChild(modalOverlay);

            // Add event listeners
            closeBtn.addEventListener('click', closeModal);
            modalOverlay.addEventListener('click', function(e) {
                if (e.target === modalOverlay) {
                    closeModal();
                }
            });

            // Remove loading indicator when iframe loads
            iframe.addEventListener('load', function() {
                iframeLoaded = true;
                loading.style.display = 'none';
            });
        }

        // Open modal
        window.openModal = function() {
            console.log('Opening modal...'); // Debug log
            createModal(); // Create modal if it doesn't exist

            modalOverlay.style.display = 'flex';
            isModalOpen = true;
            document.body.style.overflow = 'hidden';

            // Trigger fade-in animation
            requestAnimationFrame(() => {
                modalOverlay.style.opacity = '1';
                modalContent.style.transform = 'scale(1)';
            });
        }

        // Close modal
        window.closeModal = function() {
            if (!modalOverlay) return;

            // Trigger fade-out animation
            modalOverlay.style.opacity = '0';
            modalContent.style.transform = 'scale(0.9)';

            // Hide modal after animation completes
            setTimeout(() => {
                modalOverlay.style.display = 'none';
                isModalOpen = false;
                document.body.style.overflow = '';
            }, 300);
        }

        // Remove loading indicator when iframe loads
        iframe.addEventListener('load', function() {
            iframeLoaded = true;
            loading.style.display = 'none';
        });

        // Event listeners
        launchBtn.addEventListener('click', openModal);
        closeBtn.addEventListener('click', closeModal);

        // Click outside modal content to close
        modalOverlay.addEventListener('click', function(e) {
            if (e.target === modalOverlay) {
                closeModal();
            }
        });

        // Keyboard shortcuts
        document.addEventListener('keydown', function(e) {
            // ESC to close modal
            if (e.key === 'Escape' && isModalOpen) {
                closeModal();
            }
        });

        // Handle iframe communication
        window.addEventListener('message', function(event) {
            // Verify origin for security
            if (event.origin !== 'https://quote-gen.bloodandtreasure.com') {
                return;
            }

            // Handle messages from the embedded app
            console.log('Message from embedded app:', event.data);

            // Handle specific message types
            if (event.data && typeof event.data === 'object') {
                switch (event.data.type) {
                    case 'close':
                        closeModal();
                        break;
                    case 'resize':
                        // Handle resize requests from embedded app
                        break;
                }
            }
        });

        // Event listeners
        launchBtn.addEventListener('click', openModal);

        // Expose API for external control
        window.QuoteGeneratorWidget = {
            open: openModal,
            close: closeModal,
            isOpen: () => isModalOpen,
            reload: () => {
                if (iframe && iframeLoaded) {
                    loading.style.display = 'flex';
                    iframe.src = iframe.src;
                    iframeLoaded = false;
                }
            }
        };
    </script>
</div>
