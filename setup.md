# Quick Setup Guide

## 1. Install Node.js
First, install Node.js from https://nodejs.org/ (choose the LTS version)

## 2. Verify Installation
```bash
node --version
npm --version
```

## 3. Install Dependencies
```bash
# In the ai-chatbot directory
npm install
npm run install:backend
```

## 4. Configure OpenAI API Key
```bash
# Edit backend/.env and add your OpenAI API key
OPENAI_API_KEY=your_actual_api_key_here
```

## 5. Run the Application
```bash
npm run dev
```

This will start:
- Frontend at http://localhost:5173
- Backend at http://localhost:3001

## Current Features Working:
✅ Text input with example placeholders  
✅ Image upload with preview  
✅ Format selection  
✅ Responsive design  
⏳ Backend API (requires Node.js installation)  
⏳ OpenAI integration (requires API key)  

## Next Steps:
1. Install Node.js on your system
2. Add your OpenAI API key to backend/.env
3. Run `npm run dev` to start both servers
