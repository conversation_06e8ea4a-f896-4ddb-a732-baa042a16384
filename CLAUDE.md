# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Project Overview

This is an AI-powered chatbot application for estimating custom software projects with intelligent conversation management. The application uses React TypeScript frontend with a Node.js Express backend that integrates with OpenAI's API.

**Key Innovation**: The AI includes sophisticated conversation analysis that detects comprehensive responses (like "all of those things") and automatically switches from questioning mode to solution mode, preventing endless question loops.

## Architecture

### Monorepo Structure
- **Frontend**: React 18 + TypeScript 5 + Vite (root directory)
- **Backend**: Node.js + Express + TypeScript (backend/ directory)  
- **Shared**: Both use ES modules (`"type": "module"`)

### Frontend State Management Pattern
The application uses a dual-hook pattern for state management:
- `useStateManager`: Manages UI state (loading, dropdowns, format selection, assumption mode)
- `useChatHistory`: Manages conversation persistence with localStorage, handles conversation threading and follow-up messages

### Conversation Intelligence Architecture
The backend includes a sophisticated conversation analysis system in `backend/prompts/generators.ts`:
- **Conversation State Analysis**: Tracks question count, response patterns, and user frustration indicators
- **Dynamic Prompt Generation**: Creates context-aware system prompts based on conversation state
- **Response Detection**: Identifies comprehensive responses using pattern matching (e.g., "all of those things", "everything you mentioned")
- **Mode Switching**: Automatically transitions between questioning mode and solution mode

### API Communication Pattern
The frontend `ApiService` (singleton pattern) handles:
- Conversation history preservation across requests
- Context-aware request building (includes full message history)
- Separate endpoints for initial chat, follow-up, and image uploads
- Comprehensive error handling with user feedback

## Development Commands

### Root Level Commands
- `npm run dev` - Start both frontend (port 5173) and backend (port 3001) concurrently
- `npm run dev:frontend` - Start only Vite dev server
- `npm run dev:backend` - Start only backend with tsx watch
- `npm run build` - Build frontend for production
- `npm run type-check` - TypeScript type checking without emit
- `npm install:backend` - Install backend dependencies

### Backend Commands (from backend/ directory)
- `npm run dev` - Development server with tsx watch
- `npm run build` - Compile TypeScript to JavaScript
- `npm start` - Production server (build + run)

## Key Technologies & Patterns

### Frontend Stack
- **React 18** with functional components and hooks
- **TypeScript** with strict mode and path aliases (`@/` prefix)
- **Vite** for build tooling with React plugin
- **SCSS Modules** with CSS variables for theming
- **Lucide React** for icons

### Backend Stack  
- **Express** server with TypeScript compilation
- **OpenAI API** (GPT-4) with conversation context
- **Multer** for file upload handling
- **CORS** middleware for cross-origin requests

### Backend Services Architecture
- **ConversationService**: In-memory conversation storage using Map data structure
- **ChatService**: Main business logic for processing chat requests and conversation state analysis
- **FileService**: Handles image upload processing and temporary storage
- **StorageService**: Manages conversation persistence to JSON files in `backend/chats/`

### Frontend Hook Architecture
- **useStateManager**: Manages UI state with callback-optimized state updates
- **useChatHistory**: Handles conversation persistence with localStorage, includes automatic cleanup (50 conversation limit)
- **useNotification**: Toast notification system with auto-cleanup
- **useUnsplashBackground**: Background image management with proper attribution

## Environment Setup

### Frontend (.env in root)
```
VITE_UNSPLASH_ACCESS_KEY=your_key_here  # Optional for backgrounds
```

### Backend (.env in backend/)
```
OPENAI_API_KEY=your_key_here  # Required
PORT=3001                     # Optional, defaults to 3001
```

## Code Conventions

### TypeScript Configuration
- Uses strict mode with `noUnusedLocals` and `noUnusedParameters`
- Path aliases configured for cleaner imports
- React JSX transform enabled

### Component Patterns
- Functional components with TypeScript interfaces
- Props destructuring with explicit typing
- Custom hooks for reusable logic
- CSS Modules for scoped styling

### API Communication
- All API calls go through `src/services/api.ts`
- Conversation context is maintained across requests
- Error handling with user-friendly notifications

## Testing & Quality

There are no configured testing frameworks. When adding tests:
- Check for existing test scripts in package.json
- Look for test configuration files
- Ask user for preferred testing approach

## Important Implementation Details

### Conversation Flow Logic
- After exactly 3 questions asked by AI, the system automatically shows end prompt and generates comprehensive quote
- Conversation state is tracked across both frontend (localStorage) and backend (in-memory Map + JSON persistence)
- System detects user frustration patterns and comprehensive responses to switch modes appropriately

### File Structure Conventions
- All frontend imports use `@/` path aliases (configured in vite.config.ts)
- Backend uses ES modules with `.js` import extensions for TypeScript files
- Conversation history stored in `backend/chats/` as timestamped JSON files
- Image uploads temporarily stored in `backend/uploads/` directory

### API Endpoints
- `/api/chat` - Initial chat requests with format selection
- `/api/followup` - Follow-up questions within existing conversations  
- `/api/upload` - Image upload with analysis capabilities
- `/api/health` - Health check endpoint

### State Management Patterns
- Frontend uses singleton ApiService pattern for consistent API communication
- Conversation IDs follow pattern: `chat_${timestamp}_${randomId}` or `conv_${timestamp}_${randomId}`
- All state updates use useCallback optimization to prevent unnecessary re-renders