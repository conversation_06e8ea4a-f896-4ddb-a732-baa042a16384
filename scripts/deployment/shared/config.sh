#!/bin/bash

# AWS Deployment Configuration
# This file contains configuration variables used by the deployment scripts

# Prevent multiple sourcing
if [ "${CONFIG_SH_LOADED:-false}" = "true" ]; then
    return 0
fi
export CONFIG_SH_LOADED="true"

# Source global functions and variables
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
source "$SCRIPT_DIR/global.sh"

# =============================================================================
# FRONTEND CONFIGURATION
# =============================================================================

# S3 Configuration
export BUCKET_NAME="quote-gen.bloodandtreasure.com"
export AWS_REGION="us-east-1"  # CloudFront requires certificates in us-east-1
export BUILD_DIR="dist"

# CloudFront Configuration
export CLOUDFRONT_COMMENT="Frontend Distribution for $BUCKET_NAME"
export CLOUDFRONT_PRICE_CLASS="PriceClass_100"  # Use only North America and Europe edge locations

# Cache Configuration
export STATIC_CACHE_CONTROL="public, max-age=31536000"  # 1 year for static assets
export HTML_CACHE_CONTROL="public, max-age=0, must-revalidate"  # No cache for HTML/JSON

# =============================================================================
# BACKEND CONFIGURATION
# =============================================================================

# Lambda Configuration
export LAMBDA_FUNCTION_NAME="quote-gen-bloodandtreasure"
export LAMBDA_RUNTIME="nodejs20.x"  # Latest LTS supported by AWS Lambda
export LAMBDA_MEMORY="512"  # MB
export LAMBDA_TIMEOUT="30"  # seconds
export LAMBDA_HANDLER="lambda-handler.handler"  # Lambda handler entry point
export BACKEND_DIR="backend"

# API Gateway Configuration
export API_GATEWAY_NAME="quote-gen-bloodandtreasure-api"
export API_GATEWAY_STAGE="prod"
export API_GATEWAY_DESCRIPTION="API Gateway for Quote Generator Backend"

# CORS Configuration
export CORS_ORIGINS="https://*.cloudfront.net,https://bloodandtreasure.com,https://*.bloodandtreasure.com,http://localhost:5173,http://localhost:3000"
export CORS_METHODS="GET,POST,PUT,DELETE,OPTIONS"
export CORS_HEADERS="Content-Type,Authorization,X-Requested-With,Accept,Origin"
export CORS_MAX_AGE="86400"  # 24 hours for preflight cache

# =============================================================================
# GENERAL CONFIGURATION
# =============================================================================

# Build Configuration
export NODE_ENV="production"

# Optional: Custom domain configuration (uncomment and configure if you have a custom domain)
# export CUSTOM_DOMAIN="your-domain.com"
# export CERTIFICATE_ARN="arn:aws:acm:us-east-1:123456789012:certificate/your-cert-id"

# Only print configuration once
if [ "${CONFIG_LOADED:-false}" != "true" ]; then
    export CONFIG_LOADED="true"
    echo "Configuration loaded:"
    echo "  Frontend Bucket: $BUCKET_NAME"
    echo "  AWS Region: $AWS_REGION"
    echo "  Frontend Build Directory: $BUILD_DIR"
    echo "  Lambda Function: $LAMBDA_FUNCTION_NAME"
    echo "  API Gateway: $API_GATEWAY_NAME"
    echo "  Backend Directory: $BACKEND_DIR"
fi
