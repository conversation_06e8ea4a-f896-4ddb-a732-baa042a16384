#!/bin/bash

# Global Functions and Variables for AWS Deployment Scripts
# This file contains common functions and variables used across all deployment scripts

# Prevent multiple sourcing
if [ "${GLOBAL_SH_LOADED:-false}" = "true" ]; then
    return 0
fi
export GLOBAL_SH_LOADED="true"

# =============================================================================
# GLOBAL VARIABLES
# =============================================================================

# Colors for output
export RED='\033[0;31m'
export GREEN='\033[0;32m'
export YELLOW='\033[1;33m'
export BLUE='\033[0;34m'
export NC='\033[0m' # No Color

# Script directory detection
export SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
export PROJECT_ROOT="$(cd "$SCRIPT_DIR/../../.." && pwd)"

# =============================================================================
# LOGGING FUNCTIONS
# =============================================================================

# Logging functions with consistent formatting
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

log_debug() {
    if [ "${DEBUG:-false}" = "true" ]; then
        echo -e "${YELLOW}[DEBUG]${NC} $1"
    fi
}

# =============================================================================
# AWS UTILITY FUNCTIONS
# =============================================================================

# Check if AWS CLI is installed and configured
check_aws_cli() {
    if ! command -v aws &> /dev/null; then
        log_error "AWS CLI is not installed. Please install it first."
        log_info "Install AWS CLI: https://docs.aws.amazon.com/cli/latest/userguide/getting-started-install.html"
        return 1
    fi
    
    if ! aws sts get-caller-identity &>/dev/null; then
        log_error "AWS CLI is not configured or credentials are invalid."
        log_info "Configure AWS CLI: aws configure"
        return 1
    fi
    
    local identity=$(aws sts get-caller-identity)
    local account=$(echo "$identity" | jq -r '.Account')
    local user_arn=$(echo "$identity" | jq -r '.Arn')
    
    log_success "AWS CLI configured. Account: $account, User: $user_arn"
    return 0
}

# Get AWS account ID
get_aws_account_id() {
    aws sts get-caller-identity --query 'Account' --output text
}

# Check if AWS resource exists
resource_exists() {
    local resource_type=$1
    local resource_name=$2
    local region=${3:-$AWS_REGION}
    
    case $resource_type in
        "s3-bucket")
            aws s3api head-bucket --bucket "$resource_name" 2>/dev/null
            ;;
        "lambda-function")
            aws lambda get-function --function-name "$resource_name" --region "$region" 2>/dev/null
            ;;
        "api-gateway")
            local api_id=$(aws apigateway get-rest-apis --query "items[?name=='$resource_name'].id" --output text --region "$region" 2>/dev/null)
            [ -n "$api_id" ] && [ "$api_id" != "None" ]
            ;;
        "iam-role")
            aws iam get-role --role-name "$resource_name" 2>/dev/null
            ;;
        "cloudfront-distribution")
            aws cloudfront list-distributions --query "DistributionList.Items[?Comment=='$resource_name'].Id" --output text 2>/dev/null | grep -q .
            ;;
        *)
            log_error "Unknown resource type: $resource_type"
            return 1
            ;;
    esac
}

# Wait for AWS resource to be ready
wait_for_resource() {
    local resource_type=$1
    local resource_identifier=$2
    local max_wait=${3:-300}  # Default 5 minutes
    local check_interval=${4:-10}  # Default 10 seconds
    
    log_info "Waiting for $resource_type to be ready..."
    
    local elapsed=0
    while [ $elapsed -lt $max_wait ]; do
        case $resource_type in
            "lambda-function")
                local state=$(aws lambda get-function-configuration --function-name "$resource_identifier" --query 'State' --output text 2>/dev/null)
                if [ "$state" = "Active" ]; then
                    log_success "$resource_type is ready"
                    return 0
                fi
                ;;
            "cloudfront-distribution")
                local status=$(aws cloudfront get-distribution --id "$resource_identifier" --query 'Distribution.Status' --output text 2>/dev/null)
                if [ "$status" = "Deployed" ]; then
                    log_success "$resource_type is ready"
                    return 0
                fi
                ;;
            *)
                log_error "Unknown resource type for waiting: $resource_type"
                return 1
                ;;
        esac
        
        sleep $check_interval
        elapsed=$((elapsed + check_interval))
        log_debug "Waiting... ($elapsed/${max_wait}s)"
    done
    
    log_warning "$resource_type did not become ready within $max_wait seconds"
    return 1
}

# =============================================================================
# FILE SYSTEM UTILITIES
# =============================================================================

# Create temporary directory
create_temp_dir() {
    local temp_dir=$(mktemp -d)
    echo "$temp_dir"
}

# Clean up temporary files
cleanup_temp() {
    local temp_path=$1
    if [ -n "$temp_path" ] && [ -d "$temp_path" ]; then
        rm -rf "$temp_path"
        log_debug "Cleaned up temporary directory: $temp_path"
    fi
}

# Check if directory exists and is not empty
check_directory() {
    local dir_path=$1
    local dir_name=${2:-"Directory"}
    
    if [ ! -d "$dir_path" ]; then
        log_error "$dir_name does not exist: $dir_path"
        return 1
    fi
    
    if [ -z "$(ls -A "$dir_path" 2>/dev/null)" ]; then
        log_warning "$dir_name is empty: $dir_path"
        return 1
    fi
    
    log_success "$dir_name exists and is not empty: $dir_path"
    return 0
}

# =============================================================================
# VALIDATION FUNCTIONS
# =============================================================================

# Validate required environment variables
validate_env_vars() {
    local vars=("$@")
    local missing_vars=()
    
    for var in "${vars[@]}"; do
        if [ -z "${!var}" ]; then
            missing_vars+=("$var")
        fi
    done
    
    if [ ${#missing_vars[@]} -gt 0 ]; then
        log_error "Missing required environment variables:"
        for var in "${missing_vars[@]}"; do
            log_error "  - $var"
        done
        return 1
    fi
    
    return 0
}

# Validate JSON format
validate_json() {
    local json_string=$1
    if echo "$json_string" | jq . >/dev/null 2>&1; then
        return 0
    else
        return 1
    fi
}

# =============================================================================
# PACKAGE MANAGEMENT
# =============================================================================

# Check if npm package is installed
check_npm_package() {
    local package_name=$1
    local package_dir=${2:-"."}
    
    cd "$package_dir" || return 1
    npm list "$package_name" &>/dev/null
}

# Install npm package if not present
ensure_npm_package() {
    local package_name=$1
    local package_dir=${2:-"."}
    local install_type=${3:-"--save"}  # --save, --save-dev, or --global
    
    cd "$package_dir" || return 1
    
    if ! check_npm_package "$package_name" "$package_dir"; then
        log_info "Installing npm package: $package_name"
        npm install $install_type "$package_name"
    else
        log_debug "npm package already installed: $package_name"
    fi
}

# =============================================================================
# DEPLOYMENT UTILITIES
# =============================================================================

# Create deployment package (ZIP)
create_zip_package() {
    local source_dir=$1
    local output_file=$2
    local exclude_patterns=${3:-"*.ts *.map tsconfig.json"}
    
    if [ ! -d "$source_dir" ]; then
        log_error "Source directory does not exist: $source_dir"
        return 1
    fi
    
    local temp_dir=$(create_temp_dir)
    
    # Copy source files
    cp -r "$source_dir/"* "$temp_dir/"
    
    # Create ZIP package
    cd "$temp_dir" || return 1
    
    if [ -n "$exclude_patterns" ]; then
        zip -r "$output_file" . -x $exclude_patterns > /dev/null
    else
        zip -r "$output_file" . > /dev/null
    fi
    
    cd - > /dev/null
    cleanup_temp "$temp_dir"
    
    if [ -f "$output_file" ]; then
        log_success "Created deployment package: $output_file"
        return 0
    else
        log_error "Failed to create deployment package"
        return 1
    fi
}

# =============================================================================
# ERROR HANDLING
# =============================================================================

# Set up error handling
setup_error_handling() {
    set -e  # Exit on any error
    set -u  # Exit on undefined variable
    set -o pipefail  # Exit on pipe failure
    
    # Trap errors and cleanup
    trap 'handle_error $? $LINENO' ERR
    trap 'cleanup_on_exit' EXIT
}

# Handle errors
handle_error() {
    local exit_code=$1
    local line_number=$2
    
    log_error "Script failed with exit code $exit_code at line $line_number"
    
    # Perform any necessary cleanup
    cleanup_on_exit
    
    exit $exit_code
}

# Cleanup on exit
cleanup_on_exit() {
    # Override this function in individual scripts for specific cleanup
    log_debug "Performing cleanup on exit"
}

# =============================================================================
# INITIALIZATION
# =============================================================================

# Initialize global environment
init_global_env() {
    # Load configuration if available
    if [ -f "$SCRIPT_DIR/config.sh" ]; then
        source "$SCRIPT_DIR/config.sh"
        log_debug "Loaded configuration from config.sh"
    fi
    
    # Set up error handling
    setup_error_handling
    
    # Validate required tools
    local required_tools=("jq" "aws")
    for tool in "${required_tools[@]}"; do
        if ! command -v "$tool" &> /dev/null; then
            log_error "Required tool not found: $tool"
            exit 1
        fi
    done
    
    log_debug "Global environment initialized"
}

# =============================================================================
# SCRIPT EXECUTION
# =============================================================================

# Only initialize if this script is being sourced, not executed directly
if [[ "${BASH_SOURCE[0]}" != "${0}" ]]; then
    init_global_env
fi
