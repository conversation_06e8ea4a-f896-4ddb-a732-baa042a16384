# Shared Deployment Utilities

This directory contains shared utilities and configuration used by all deployment scripts.

## Files

### `global.sh`
**Common functions and utilities** - Provides standardized functionality across all scripts:

#### Logging Functions
- `log_info()` - Blue informational messages
- `log_success()` - Green success messages  
- `log_warning()` - Yellow warning messages
- `log_error()` - Red error messages
- `log_debug()` - Debug messages (when DEBUG=1)

#### AWS Utilities
- `check_aws_cli()` - Validates AWS CLI installation and configuration
- `get_aws_account_id()` - Gets current AWS account ID
- `resource_exists()` - Checks if AWS resource exists
- `wait_for_resource()` - Waits for resource to be ready

#### File System Utilities
- `create_temp_dir()` - Creates temporary directory
- `cleanup_temp()` - Cleans up temporary files
- `check_directory()` - Validates directory exists

#### Validation Functions
- `validate_env_vars()` - Validates required environment variables
- `validate_json()` - Validates JSON syntax

#### Package Management
- `check_npm_package()` - Checks if npm package is installed
- `ensure_npm_package()` - Installs npm package if missing

#### Deployment Utilities
- `create_zip_package()` - Creates deployment ZIP packages

#### Error Handling
- `setup_error_handling()` - Sets up error traps
- `handle_error()` - Handles script errors
- `cleanup_on_exit()` - Cleanup on script exit

#### Initialization
- `init_global_env()` - Initializes global environment

### `config.sh`
**Configuration variables** - Centralized configuration for all deployments:

#### Frontend Configuration
- `BUCKET_NAME` - S3 bucket name
- `BUILD_DIR` - Local build directory
- `CLOUDFRONT_COMMENT` - Distribution identifier
- Cache control settings

#### Backend Configuration
- `LAMBDA_FUNCTION_NAME` - Lambda function name
- `API_GATEWAY_NAME` - API Gateway name
- `LAMBDA_RUNTIME` - Node.js runtime version
- `LAMBDA_MEMORY` - Memory allocation (512MB)
- `LAMBDA_TIMEOUT` - Function timeout (30s)
- `BACKEND_DIR` - Backend source directory

#### AWS Configuration
- `AWS_REGION` - AWS region (us-east-1)
- `API_GATEWAY_STAGE` - API stage (prod)

#### CORS Configuration
- `CORS_ORIGINS` - Allowed origins for API requests
- `CORS_HEADERS` - Allowed headers
- `CORS_METHODS` - Allowed HTTP methods

## Usage

All deployment scripts automatically source these files:

```bash
# In any deployment script
source "$SCRIPT_DIR/../shared/global.sh"

# This automatically loads:
# - All global functions
# - Configuration variables from config.sh
# - Error handling setup
# - Logging utilities
```

## Benefits

1. **Code Reuse**: Eliminates ~200 lines of duplicate code across scripts
2. **Consistency**: Standardized logging, error handling, and AWS operations
3. **Maintainability**: Single source of truth for common functionality
4. **Error Handling**: Centralized error handling and cleanup procedures
5. **Validation**: Consistent validation patterns across all scripts

## Customization

To modify deployment behavior:
1. Edit `config.sh` for configuration changes
2. Edit `global.sh` for new shared functions
3. All scripts will automatically use the updated functionality
