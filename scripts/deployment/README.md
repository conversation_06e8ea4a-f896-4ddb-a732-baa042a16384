# AWS Deployment Scripts

This directory contains scripts for deploying the AI Chatbot application to AWS, including both frontend (React) and backend (Node.js API) components.

## Directory Structure

```
scripts/deployment/
├── README.md                    # This file
├── frontend/                    # Frontend deployment scripts
│   ├── deploy-frontend.sh       # Full frontend deployment
│   ├── update-frontend.sh       # Quick frontend updates
│   └── frontend-status.sh       # Frontend status check
├── backend/                     # Backend deployment scripts
│   ├── deploy-backend.sh        # Full backend deployment
│   ├── deploy-api-gateway.sh    # API Gateway deployment
│   ├── update-backend.sh        # Quick backend updates
│   └── backend-status.sh        # Backend status check
└── shared/                      # Shared utilities
    ├── global.sh                # Common functions and utilities
    └── config.sh                # Configuration variables
```

## Architecture

### Global Functions (`global.sh`)
All scripts now use a centralized `global.sh` file that provides:
- **Logging Functions**: Consistent colored output (`log_info`, `log_success`, `log_warning`, `log_error`)
- **AWS Utilities**: Common AWS CLI operations and validation functions (`check_aws_cli`, `get_aws_account_id`, `resource_exists`, `wait_for_resource`)
- **File System Utilities**: Temporary directory management and validation (`create_temp_dir`, `cleanup_temp`, `check_directory`)
- **Error Handling**: Standardized error handling and cleanup procedures (`setup_error_handling`, `handle_error`)
- **Package Management**: npm package installation and validation (`check_npm_package`, `ensure_npm_package`)
- **Deployment Utilities**: ZIP package creation and resource management (`create_zip_package`)

### Configuration (`config.sh`)
Centralized configuration file that sources `global.sh` and provides all deployment variables.

## Available Scripts

```bash
# Backend deployment
npm run deploy:backend          # Full backend deployment
npm run deploy:backend:update   # Quick backend updates
npm run deploy:backend:status   # Check backend status

# Frontend deployment
npm run deploy:frontend         # Full frontend deployment
npm run deploy:frontend:update  # Quick frontend updates
npm run deploy:frontend:status  # Check frontend status

# Combined operations
npm run deploy:full            # Deploy both frontend and backend
npm run status:all             # Check status of all deployments
```

## Benefits of Global Functions

1. **Code Reuse**: Eliminates duplicate functions across scripts (removed ~200 lines of duplicate code)
2. **Consistency**: Standardized logging, error handling, and AWS operations
3. **Maintainability**: Single source of truth for common functionality
4. **Error Handling**: Centralized error handling and cleanup procedures
5. **Validation**: Consistent validation patterns across all scripts

The deployment system creates a complete AWS infrastructure with the following components:

### Frontend Infrastructure
- **S3 Bucket**: Stores the built React application files
- **CloudFront Distribution**: Provides global CDN with HTTPS
- **Origin Access Control (OAC)**: Secures S3 bucket access (replaces deprecated OAI)
- **S3 Bucket Policy**: Allows CloudFront to access S3 objects
- **Cache Invalidation**: Ensures immediate updates when deploying

### Backend Infrastructure
- **Lambda Function**: Runs the Node.js Express API
- **API Gateway**: Provides REST API endpoints with CORS support
- **IAM Role**: Execution role for Lambda with appropriate permissions
- **CloudWatch Logs**: Automatic logging for Lambda function

## Architecture

```
Internet → CloudFront Distribution → S3 Bucket (Frontend)
    ↓
API Gateway → Lambda Function (Backend API)
    ↓
CloudWatch Logs
```

## Prerequisites

1. **AWS CLI installed and configured**
   ```bash
   # Install AWS CLI (if not already installed)
   curl "https://awscli.amazonaws.com/AWSCLIV2.pkg" -o "AWSCLIV2.pkg"
   sudo installer -pkg AWSCLIV2.pkg -target /
   
   # Configure AWS CLI with your credentials
   aws configure
   ```

2. **Required AWS IAM Permissions**
   Your AWS user/role needs the following permissions:

   **Frontend:**
   - S3: `s3:CreateBucket`, `s3:PutObject`, `s3:PutObjectAcl`, `s3:GetObject`, `s3:DeleteObject`, `s3:ListBucket`, `s3:PutBucketPolicy`, `s3:PutBucketWebsite`, `s3:PutPublicAccessBlock`, `s3:PutBucketOwnershipControls`
   - CloudFront: `cloudfront:CreateDistribution`, `cloudfront:GetDistribution`, `cloudfront:ListDistributions`, `cloudfront:CreateInvalidation`, `cloudfront:ListInvalidations`, `cloudfront:CreateOriginAccessControl`, `cloudfront:ListOriginAccessControls`

   **Backend:**
   - Lambda: `lambda:CreateFunction`, `lambda:UpdateFunctionCode`, `lambda:UpdateFunctionConfiguration`, `lambda:GetFunction`, `lambda:AddPermission`, `lambda:RemovePermission`
   - API Gateway: `apigateway:*` (or specific permissions for CreateRestApi, CreateResource, PutMethod, CreateDeployment, etc.)
   - IAM: `iam:CreateRole`, `iam:AttachRolePolicy`, `iam:PassRole`, `iam:GetRole`
   - CloudWatch: `logs:CreateLogGroup`, `logs:DescribeLogGroups`, `logs:FilterLogEvents`
   - STS: `sts:GetCallerIdentity`

3. **Node.js and npm** (version 18+ for building and running applications)

4. **jq** (JSON processor for parsing AWS CLI responses)
   ```bash
   # Install on macOS
   brew install jq

   # Install on Ubuntu/Debian
   sudo apt-get install jq
   ```

## Scripts Overview

### Configuration
- **`config.sh`**: Central configuration file for both frontend and backend

### Frontend Scripts
- **`deploy-frontend.sh`**: Full frontend deployment (S3 + CloudFront)
- **`update-frontend.sh`**: Quick frontend updates
- **`frontend-status.sh`**: Frontend deployment status

### Backend Scripts
- **`deploy-backend.sh`**: Full backend deployment (Lambda + API Gateway)
- **`deploy-api-gateway.sh`**: API Gateway setup (called by deploy-backend.sh)
- **`update-backend.sh`**: Quick backend code updates
- **`backend-status.sh`**: Backend deployment status

### Legacy Frontend Scripts (renamed)
- **`deploy-frontend.sh`** (formerly `deploy.sh`): Full frontend infrastructure setup and deployment

**What it does:**
- Creates S3 bucket if it doesn't exist
- Configures S3 bucket for static website hosting with ACLs disabled
- Creates CloudFront distribution with Origin Access Control (OAC)
- Sets up proper S3 bucket policies for CloudFront access
- Builds the React application
- Uploads files to S3 with appropriate cache headers
- Invalidates CloudFront cache
- Returns deployment information

**Usage:**
```bash
# Make the script executable
chmod +x scripts/deploy.sh

# Run the deployment
./scripts/deploy.sh
```

### 2. `update-deployment.sh` - Quick Updates
Use this script for subsequent deployments when infrastructure already exists.

**What it does:**
- Verifies existing infrastructure
- Builds the React application
- Uploads files to S3
- Invalidates CloudFront cache

**Usage:**
```bash
# Make the script executable
chmod +x scripts/update-deployment.sh

# Run the update
./scripts/update-deployment.sh
```

### 3. `deployment-status.sh` - Status Check
Check the status of your AWS infrastructure and deployment.

**What it does:**
- Verifies AWS CLI configuration
- Checks S3 bucket status and configuration
- Checks CloudFront distribution status
- Tests website accessibility
- Shows recent invalidations

**Usage:**
```bash
# Make the script executable
chmod +x scripts/deployment-status.sh

# Check status
./scripts/deployment-status.sh
```

### 4. `config.sh` - Configuration
Contains configuration variables used by all scripts. Modify this file to customize your deployment.

## Configuration

Edit `scripts/config.sh` to customize your deployment:

```bash
# S3 Configuration
BUCKET_NAME="quote-gen.bloodandtreasure.com"
AWS_REGION="us-east-1"
BUILD_DIR="dist"

# CloudFront Configuration
CLOUDFRONT_COMMENT="Frontend Distribution for quote-gen.bloodandtreasure.com"
CLOUDFRONT_PRICE_CLASS="PriceClass_100"
```

## Typical Workflow

### First-time Setup
1. Configure your AWS credentials: `aws configure`
2. Customize `scripts/config.sh` if needed
3. Run the full deployment: `./scripts/deploy.sh`
4. Wait for CloudFront distribution to deploy (10-15 minutes)

### Subsequent Updates
1. Make changes to your React application
2. Run the update script: `./scripts/update-deployment.sh`
3. Changes will be live within a few minutes

### Monitoring
- Check deployment status: `./scripts/deployment-status.sh`
- Monitor CloudFront distribution in AWS Console
- Check S3 bucket contents in AWS Console

## Important Notes

### Security
- **ACLs are disabled** on the S3 bucket for security best practices
- **Origin Access Control (OAC)** is used instead of Origin Access Identity (OAI)
- **Bucket policies** control access instead of ACLs
- **HTTPS redirect** is enforced on CloudFront

### Caching Strategy
- **Static assets** (JS, CSS, images): Cached for 1 year
- **HTML and JSON files**: No cache, always fetch fresh
- **404 errors**: Redirect to `index.html` for SPA routing

### Cost Optimization
- Uses `PriceClass_100` (North America and Europe only) to reduce costs
- Compression is enabled to reduce bandwidth costs
- Appropriate cache headers minimize origin requests

## Troubleshooting

### Common Issues

1. **"Bucket already exists" error**
   - The bucket name must be globally unique
   - Change `BUCKET_NAME` in `config.sh`

2. **"Access Denied" errors**
   - Check your AWS IAM permissions
   - Ensure AWS CLI is configured correctly

3. **CloudFront distribution not working**
   - Wait 10-15 minutes for initial deployment
   - Check distribution status: `./scripts/deployment-status.sh`

4. **Website shows old content**
   - CloudFront cache may not be invalidated yet
   - Wait a few minutes or create manual invalidation

### Manual Commands

If you need to run individual AWS commands:

```bash
# Check bucket exists
aws s3api head-bucket --bucket quote-gen.bloodandtreasure.com

# List CloudFront distributions
aws cloudfront list-distributions --query 'DistributionList.Items[*].[Id,DomainName,Comment]' --output table

# Create manual invalidation
aws cloudfront create-invalidation --distribution-id YOUR_DISTRIBUTION_ID --paths "/*"

# Check invalidation status
aws cloudfront list-invalidations --distribution-id YOUR_DISTRIBUTION_ID
```

## Output Information

After successful deployment, you'll see:
- **S3 Bucket Name**: Where your files are stored
- **S3 Region**: AWS region of your bucket
- **CloudFront Distribution ID**: For future reference and invalidations
- **CloudFront Domain**: The CDN domain (e.g., `d123456789.cloudfront.net`)
- **Frontend URL**: The complete HTTPS URL to access your application

## Support

If you encounter issues:
1. Run `./scripts/deployment-status.sh` to diagnose problems
2. Check AWS CloudWatch logs for detailed error messages
3. Verify your AWS IAM permissions
4. Ensure your AWS CLI is up to date
