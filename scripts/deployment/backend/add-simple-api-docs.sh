#!/bin/bash

# Simple AWS API Gateway Documentation Script
# This script adds basic documentation to the API Gateway

# Get the directory where this script is located
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"

# Source global functions and configuration
source "$SCRIPT_DIR/../shared/global.sh"

# Use config variables with fallbacks
API_GATEWAY_NAME="${API_GATEWAY_NAME:-quote-gen-bloodandtreasure-api}"
API_GATEWAY_STAGE="${API_GATEWAY_STAGE:-prod}"
AWS_REGION="${AWS_REGION:-us-east-1}"

# Get API Gateway ID
get_api_gateway_id() {
    log_info "Finding API Gateway..."
    
    local api_id=$(aws apigateway get-rest-apis \
        --query "items[?name=='$API_GATEWAY_NAME'].id" \
        --output text 2>/dev/null || echo "")
    
    if [ -z "$api_id" ] || [ "$api_id" = "None" ]; then
        log_error "API Gateway '$API_GATEWAY_NAME' not found. Please deploy the API first."
        exit 1
    fi
    
    log_success "Found API Gateway: $API_GATEWAY_NAME (ID: $api_id)"
    echo "$api_id"
}

# Add API documentation
add_api_documentation() {
    local api_id=$1
    
    log_info "Adding API documentation..."
    
    # Add API-level documentation
    aws apigateway create-documentation-part \
        --rest-api-id "$api_id" \
        --location type=API \
        --properties '{"description":"AI Chatbot API for processing chat requests, image analysis, and project estimation","version":"1.0.0"}' \
        2>/dev/null || log_warning "API documentation may already exist"
    
    # Add method documentation for health endpoint
    aws apigateway create-documentation-part \
        --rest-api-id "$api_id" \
        --location type=METHOD,path="/api/health",method=GET \
        --properties '{"summary":"Health Check","description":"Returns API health status"}' \
        2>/dev/null || log_warning "Health endpoint documentation may already exist"
    
    # Add method documentation for chat endpoint
    aws apigateway create-documentation-part \
        --rest-api-id "$api_id" \
        --location type=METHOD,path="/api/chat",method=POST \
        --properties '{"summary":"Process Chat","description":"Process text-based chat requests with AI assistant"}' \
        2>/dev/null || log_warning "Chat endpoint documentation may already exist"
    
    # Add method documentation for followup endpoint
    aws apigateway create-documentation-part \
        --rest-api-id "$api_id" \
        --location type=METHOD,path="/api/followup",method=POST \
        --properties '{"summary":"Follow-up Chat","description":"Process follow-up requests within conversation context"}' \
        2>/dev/null || log_warning "Followup endpoint documentation may already exist"
    
    # Add method documentation for upload endpoint
    aws apigateway create-documentation-part \
        --rest-api-id "$api_id" \
        --location type=METHOD,path="/api/upload",method=POST \
        --properties '{"summary":"Image Upload","description":"Upload and analyze images for project estimation"}' \
        2>/dev/null || log_warning "Upload endpoint documentation may already exist"
    
    log_success "Documentation parts added successfully"
}

# Create documentation version
create_documentation_version() {
    local api_id=$1
    
    log_info "Creating documentation version..."
    
    local version_name="v1.0.0"
    local description="API Documentation - $(date)"
    
    # Delete existing version if it exists
    aws apigateway delete-documentation-version \
        --rest-api-id "$api_id" \
        --documentation-version "$version_name" \
        2>/dev/null || true
    
    # Create new version
    aws apigateway create-documentation-version \
        --rest-api-id "$api_id" \
        --documentation-version "$version_name" \
        --description "$description" \
        2>/dev/null && log_success "Documentation version created: $version_name" || log_warning "Failed to create documentation version"
}

# Export documentation
export_documentation() {
    local api_id=$1
    
    log_info "Exporting API documentation..."
    
    # Export as OpenAPI/Swagger
    aws apigateway get-export \
        --rest-api-id "$api_id" \
        --stage-name "$API_GATEWAY_STAGE" \
        --export-type swagger \
        --parameters extensions=documentation \
        api-documentation.json 2>/dev/null && \
        log_success "Documentation exported to api-documentation.json" || \
        log_warning "Failed to export documentation"
}

# Main function
main() {
    log_info "Adding documentation to API Gateway..."
    log_info "API Gateway: $API_GATEWAY_NAME"
    log_info "Stage: $API_GATEWAY_STAGE"
    echo
    
    # Get API Gateway ID
    local api_id=$(get_api_gateway_id)
    
    # Add documentation
    add_api_documentation "$api_id"
    
    # Create documentation version
    create_documentation_version "$api_id"
    
    # Export documentation
    export_documentation "$api_id"
    
    # Get API endpoint
    local api_endpoint="https://$api_id.execute-api.$AWS_REGION.amazonaws.com/$API_GATEWAY_STAGE"
    
    echo
    log_success "API Gateway documentation completed!"
    echo
    echo "=== API INFORMATION ==="
    echo "API Gateway ID: $api_id"
    echo "API Gateway Name: $API_GATEWAY_NAME"
    echo "Stage: $API_GATEWAY_STAGE"
    echo "Endpoint URL: $api_endpoint"
    echo
    echo "=== AVAILABLE ENDPOINTS ==="
    echo "GET  $api_endpoint/api/health     - Health check"
    echo "POST $api_endpoint/api/chat       - Process chat requests"
    echo "POST $api_endpoint/api/followup   - Process follow-up requests"
    echo "POST $api_endpoint/api/upload     - Upload and analyze images"
    echo
    echo "=== DOCUMENTATION ACCESS ==="
    echo "AWS Console: https://console.aws.amazon.com/apigateway/home?region=$AWS_REGION#/apis/$api_id/documentation"
    if [ -f "api-documentation.json" ]; then
        echo "Exported File: ./api-documentation.json"
    fi
    echo
    log_info "View documentation in AWS Console or import the JSON into Postman/Insomnia"
}

# Run main function
main "$@"
