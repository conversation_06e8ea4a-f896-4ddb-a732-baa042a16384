# Backend Deployment Scripts

This directory contains scripts for deploying and managing the Node.js backend API to AWS Lambda and API Gateway.

## Scripts

### `deploy-backend.sh`
**Full backend deployment** - Creates complete AWS infrastructure:
- Lambda function with Node.js 20.x runtime
- IAM role with proper permissions
- API Gateway REST API with Lambda integration
- Comprehensive CORS configuration
- Builds and deploys the TypeScript backend

**Usage:**
```bash
./deploy-backend.sh
# or via npm
npm run deploy:backend
```

### `deploy-api-gateway.sh`
**API Gateway deployment** - Specialized script for API Gateway setup:
- Creates REST API with proper configuration
- Sets up Lambda proxy integration
- Configures CORS for multiple domains
- Deploys to production stage

**Usage:**
```bash
./deploy-api-gateway.sh
```

### `update-backend.sh`
**Quick backend updates** - Updates existing Lambda function:
- Builds TypeScript backend
- Creates deployment package
- Updates Lambda function code
- Tests the updated function
- Much faster than full deployment

**Usage:**
```bash
./update-backend.sh
# or via npm
npm run deploy:backend:update
```

### `backend-status.sh`
**Status check** - Checks the health of backend infrastructure:
- Lambda function status and configuration
- API Gateway deployment status
- IAM role and permissions
- Endpoint health checks
- Recent function metrics

**Usage:**
```bash
./backend-status.sh
# or via npm
npm run deploy:backend:status
```

### `add-simple-api-docs.sh`
**API documentation setup** - Adds comprehensive documentation to API Gateway:
- Creates API-level documentation
- Documents all endpoints with descriptions
- Creates documentation versions
- Exports documentation as OpenAPI/Swagger JSON

**Usage:**
```bash
./add-simple-api-docs.sh
# or via npm
npm run docs:add
```

### `view-api-documentation.sh`
**View API documentation** - Displays current API documentation:
- Shows documentation summary
- Lists documented endpoints
- Provides AWS Console links
- Exports current documentation
- Includes testing examples

**Usage:**
```bash
./view-api-documentation.sh
# or via npm
npm run docs:view
```

## Infrastructure Created

- **Lambda Function**: `quote-gen-bloodandtreasure`
  - Node.js 20.x runtime
  - 512MB memory, 30-second timeout
  - Express.js app wrapped with serverless-express
  - Automatic scaling and high availability

- **API Gateway**: `quote-gen-bloodandtreasure-api`
  - REST API with Lambda proxy integration
  - Production stage deployment
  - CORS enabled for multiple domains
  - Endpoint: `https://{api-id}.execute-api.us-east-1.amazonaws.com/prod`

- **IAM Role**: `quote-gen-bloodandtreasure-lambda-role`
  - Basic Lambda execution permissions
  - CloudWatch Logs access

## CORS Configuration

The API is configured to accept requests from:
- `*.cloudfront.net` (for frontend)
- `bloodandtreasure.com`
- `*.bloodandtreasure.com`
- `localhost:*` (for development)

## Prerequisites

- AWS CLI installed and configured
- Node.js and npm installed
- TypeScript compiler
- Proper AWS permissions for Lambda, API Gateway, and IAM

## Configuration

All configuration is managed in `../shared/config.sh`. Key variables:
- `LAMBDA_FUNCTION_NAME`: Lambda function name
- `API_GATEWAY_NAME`: API Gateway name
- `LAMBDA_RUNTIME`: Node.js runtime version
- `LAMBDA_MEMORY`: Memory allocation
- `LAMBDA_TIMEOUT`: Function timeout
- CORS domain settings
