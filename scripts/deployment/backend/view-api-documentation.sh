#!/bin/bash

# View API Gateway Documentation Script
# This script displays the current API documentation and provides access links

# Get the directory where this script is located
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"

# Source global functions and configuration
source "$SCRIPT_DIR/../shared/global.sh"

# Use config variables with fallbacks
API_GATEWAY_NAME="${API_GATEWAY_NAME:-quote-gen-bloodandtreasure-api}"
API_GATEWAY_STAGE="${API_GATEWAY_STAGE:-prod}"
AWS_REGION="${AWS_REGION:-us-east-1}"

# Get API Gateway ID
get_api_gateway_id() {
    local api_id=$(aws apigateway get-rest-apis \
        --query "items[?name=='$API_GATEWAY_NAME'].id" \
        --output text 2>/dev/null || echo "")
    
    if [ -z "$api_id" ] || [ "$api_id" = "None" ]; then
        log_error "API Gateway '$API_GATEWAY_NAME' not found."
        exit 1
    fi
    
    echo "$api_id"
}

# Display documentation summary
show_documentation_summary() {
    local api_id=$1
    
    log_info "API Documentation Summary"
    echo
    
    # Get documentation parts count
    local doc_count=$(aws apigateway get-documentation-parts \
        --rest-api-id "$api_id" \
        --query 'length(items)' --output text 2>/dev/null || echo "0")
    
    if [ "$doc_count" -eq 0 ]; then
        log_warning "No documentation found for this API"
        echo "Run './add-simple-api-docs.sh' to add documentation"
        return
    fi
    
    log_success "Found $doc_count documentation parts"
    echo
    
    # Show documentation parts
    echo "=== DOCUMENTED ENDPOINTS ==="
    aws apigateway get-documentation-parts \
        --rest-api-id "$api_id" \
        --query 'items[?location.type==`METHOD`].[location.method,location.path,properties]' \
        --output table 2>/dev/null || echo "Failed to retrieve documentation details"
    
    echo
}

# Show documentation versions
show_documentation_versions() {
    local api_id=$1
    
    log_info "Documentation Versions"
    echo
    
    aws apigateway get-documentation-versions \
        --rest-api-id "$api_id" \
        --query 'items[].{Version:version,Created:createdDate,Description:description}' \
        --output table 2>/dev/null || log_warning "No documentation versions found"
    
    echo
}

# Export current documentation
export_current_documentation() {
    local api_id=$1
    
    log_info "Exporting current documentation..."
    
    local output_file="api-documentation-$(date +%Y%m%d-%H%M%S).json"
    
    aws apigateway get-export \
        --rest-api-id "$api_id" \
        --stage-name "$API_GATEWAY_STAGE" \
        --export-type swagger \
        --parameters extensions=documentation \
        "$output_file" 2>/dev/null && \
        log_success "Documentation exported to: $output_file" || \
        log_warning "Failed to export documentation"
}

# Main function
main() {
    log_info "Viewing API Gateway Documentation"
    log_info "API Gateway: $API_GATEWAY_NAME"
    log_info "Stage: $API_GATEWAY_STAGE"
    echo
    
    # Get API Gateway ID
    local api_id=$(get_api_gateway_id)
    local api_endpoint="https://$api_id.execute-api.$AWS_REGION.amazonaws.com/$API_GATEWAY_STAGE"
    
    # Show documentation summary
    show_documentation_summary "$api_id"
    
    # Show documentation versions
    show_documentation_versions "$api_id"
    
    # Export documentation
    export_current_documentation "$api_id"
    
    echo
    echo "=== API INFORMATION ==="
    echo "API Gateway ID: $api_id"
    echo "API Gateway Name: $API_GATEWAY_NAME"
    echo "Stage: $API_GATEWAY_STAGE"
    echo "Endpoint URL: $api_endpoint"
    echo
    echo "=== AVAILABLE ENDPOINTS ==="
    echo "GET  $api_endpoint/api/health     - Health check"
    echo "POST $api_endpoint/api/chat       - Process chat requests"
    echo "POST $api_endpoint/api/followup   - Process follow-up requests"
    echo "POST $api_endpoint/api/upload     - Upload and analyze images"
    echo
    echo "=== DOCUMENTATION ACCESS ==="
    echo "AWS Console: https://console.aws.amazon.com/apigateway/home?region=$AWS_REGION#/apis/$api_id/documentation"
    echo "API Testing: https://console.aws.amazon.com/apigateway/home?region=$AWS_REGION#/apis/$api_id/test"
    echo
    echo "=== TESTING EXAMPLES ==="
    echo "Health Check:"
    echo "  curl $api_endpoint/api/health"
    echo
    echo "Chat Request:"
    echo "  curl -X POST $api_endpoint/api/chat \\"
    echo "    -H 'Content-Type: application/json' \\"
    echo "    -d '{\"messages\":[{\"role\":\"user\",\"content\":\"Hello\"}]}'"
    echo
    log_info "Import the exported JSON into Postman, Insomnia, or other API tools"
}

# Run main function
main "$@"
