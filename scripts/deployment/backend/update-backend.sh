#!/bin/bash

# AWS Backend Update Script
# This script updates the Lambda function code without recreating infrastructure

# Get the directory where this script is located
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"

# Source global functions and configuration
source "$SCRIPT_DIR/../shared/global.sh"

# Use config variables with fallbacks
LAMBDA_FUNCTION_NAME="${LAMBDA_FUNCTION_NAME:-quote-gen-bloodandtreasure}"
LAMBDA_HANDLER="${LAMBDA_HANDLER:-dist/lambda-handler.handler}"
BACKEND_DIR="${BACKEND_DIR:-backend}"

# Note: Colors and logging functions are now provided by global.sh

# Check if Lambda function exists
check_lambda_exists() {
    log_info "Checking if Lambda function exists..."
    
    if ! aws lambda get-function --function-name "$LAMBDA_FUNCTION_NAME" &>/dev/null; then
        log_error "Lambda function '$LAMBDA_FUNCTION_NAME' not found."
        log_error "Please run the full deployment first: ./scripts/deploy-backend.sh"
        exit 1
    fi
    
    log_success "Lambda function found"
}

# Build backend for Lambda deployment
build_backend() {
    log_info "Building backend for Lambda deployment..."
    
    cd "$PROJECT_ROOT/$BACKEND_DIR"
    
    # Install all dependencies for building
    log_info "Installing all dependencies for building..."
    npm install

    # Build TypeScript (skip if dist exists and is recent)
    if [ ! -d "dist" ] || [ ! -f "dist/server.js" ]; then
        log_info "Building TypeScript..."
        npm run build
    else
        log_info "Using existing TypeScript build..."
    fi

    # Install production dependencies only
    log_info "Installing production dependencies only..."
    npm install --only=production
    
    # Create Lambda handler wrapper
    log_info "Creating Lambda handler wrapper..."
    cat > dist/lambda-handler.js << 'EOF'
import serverlessExpress from '@vendia/serverless-express';
import express from 'express';
import dotenv from 'dotenv';
import { corsMiddleware } from './middleware/corsMiddleware.js';
import { errorMiddleware } from './middleware/errorMiddleware.js';
import routes from './routes/index.js';

// Set production environment for Lambda
process.env.NODE_ENV = 'production';

dotenv.config();

const app = express();

// Middleware
app.use(corsMiddleware);
app.use(express.json());

// Routes
app.use(routes);

// Error handling middleware
app.use(errorMiddleware);

// Export Lambda handler
export const handler = serverlessExpress({ app });
EOF
    
    # Ensure serverless-express dependency exists
    if ! npm list @vendia/serverless-express &>/dev/null; then
        log_info "Adding serverless-express dependency..."
        npm install @vendia/serverless-express
    fi
    
    cd "$PROJECT_ROOT"
    log_success "Backend built successfully"
}

# Create deployment package
create_deployment_package() {
    log_info "Creating deployment package..." >&2

    local temp_dir=$(mktemp -d)
    local package_file="$PROJECT_ROOT/lambda-update.zip"

    # Copy built files
    cp -r "$PROJECT_ROOT/$BACKEND_DIR/dist/"* "$temp_dir/"
    cp -r "$PROJECT_ROOT/$BACKEND_DIR/node_modules" "$temp_dir/"
    cp "$PROJECT_ROOT/$BACKEND_DIR/package.json" "$temp_dir/"

    # Create zip package
    cd "$temp_dir"
    zip -r "$package_file" . -x "*.ts" "*.map" "tsconfig.json" > /dev/null

    # Cleanup
    rm -rf "$temp_dir"
    cd "$PROJECT_ROOT"

    log_success "Deployment package created: lambda-update.zip" >&2
    echo "$package_file"
}

# Update Lambda function code
update_lambda_function() {
    local package_file=$1
    
    log_info "Updating Lambda function code..."
    
    # Update function code
    aws lambda update-function-code \
        --function-name "$LAMBDA_FUNCTION_NAME" \
        --zip-file "fileb://$package_file" > /dev/null

    # Wait for code update to complete before updating configuration
    log_info "Waiting for code update to complete..."
    aws lambda wait function-updated --function-name "$LAMBDA_FUNCTION_NAME"

    # Update function configuration to ensure correct handler
    aws lambda update-function-configuration \
        --function-name "$LAMBDA_FUNCTION_NAME" \
        --handler "$LAMBDA_HANDLER" > /dev/null

    log_success "Lambda function code and configuration updated"
    
    # Wait for function to be ready
    log_info "Waiting for Lambda function to be ready..."
    aws lambda wait function-updated --function-name "$LAMBDA_FUNCTION_NAME"
    
    log_success "Lambda function is ready"
}

# Invalidate API Gateway cache (if caching is enabled)
invalidate_api_cache() {
    log_info "Checking for API Gateway cache invalidation..."
    
    # Get API Gateway ID
    local api_id=$(aws apigateway get-rest-apis \
        --query "items[?name=='$API_GATEWAY_NAME'].id" \
        --output text 2>/dev/null || echo "")
    
    if [ -n "$api_id" ] && [ "$api_id" != "None" ]; then
        log_info "Found API Gateway: $api_id"
        
        # Check if stage has caching enabled
        local cache_enabled=$(aws apigateway get-stage \
            --rest-api-id "$api_id" \
            --stage-name "$API_GATEWAY_STAGE" \
            --query 'cacheClusterEnabled' \
            --output text 2>/dev/null || echo "false")
        
        if [ "$cache_enabled" = "true" ]; then
            log_info "Flushing API Gateway cache..."
            aws apigateway flush-stage-cache \
                --rest-api-id "$api_id" \
                --stage-name "$API_GATEWAY_STAGE" > /dev/null
            log_success "API Gateway cache flushed"
        else
            log_info "API Gateway caching not enabled, no cache to flush"
        fi
    else
        log_warning "API Gateway not found, skipping cache invalidation"
    fi
}

# Test the updated function
test_function() {
    log_info "Testing updated Lambda function..."
    
    # Get API Gateway endpoint
    local api_id=$(aws apigateway get-rest-apis \
        --query "items[?name=='$API_GATEWAY_NAME'].id" \
        --output text 2>/dev/null || echo "")
    
    if [ -n "$api_id" ] && [ "$api_id" != "None" ]; then
        local api_endpoint="https://$api_id.execute-api.$AWS_REGION.amazonaws.com/$API_GATEWAY_STAGE"
        
        log_info "Testing health endpoint: $api_endpoint/api/health"
        
        # Test with curl if available
        if command -v curl &> /dev/null; then
            local response=$(curl -s -w "%{http_code}" -o /dev/null "$api_endpoint/api/health" || echo "000")
            
            if [ "$response" = "200" ]; then
                log_success "Health check passed (HTTP $response)"
            else
                log_warning "Health check returned HTTP $response"
            fi
        else
            log_info "curl not available, skipping automatic test"
            log_info "Manual test: curl $api_endpoint/api/health"
        fi
    else
        log_warning "API Gateway not found, skipping endpoint test"
    fi
}

# Main function
main() {
    log_info "Starting backend update..."
    log_info "Lambda Function: $LAMBDA_FUNCTION_NAME"
    echo
    
    # Check if Lambda function exists
    check_lambda_exists
    
    # Build and package backend
    build_backend
    local package_file=$(create_deployment_package)
    
    # Update Lambda function
    update_lambda_function "$package_file"
    
    # Cleanup deployment package
    rm -f "$package_file"
    
    # Invalidate cache if needed
    invalidate_api_cache
    
    # Test the function
    test_function
    
    echo
    log_success "Backend update completed successfully!"
    echo
    echo "=== UPDATE INFORMATION ==="
    echo "Lambda Function: $LAMBDA_FUNCTION_NAME"
    echo "Updated: $(date)"
    echo
    
    # Get API endpoint for reference
    local api_id=$(aws apigateway get-rest-apis \
        --query "items[?name=='$API_GATEWAY_NAME'].id" \
        --output text 2>/dev/null || echo "")
    
    if [ -n "$api_id" ] && [ "$api_id" != "None" ]; then
        local api_endpoint="https://$api_id.execute-api.$AWS_REGION.amazonaws.com/$API_GATEWAY_STAGE"
        echo "API Endpoint: $api_endpoint"
        echo "Health Check: $api_endpoint/api/health"
    fi
}

# Run main function
main "$@"
