#!/bin/bash

# AWS API Gateway Deployment Script
# This script creates API Gateway REST API and integrates it with Lambda

# Get the directory where this script is located
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"

# Source global functions and configuration
source "$SCRIPT_DIR/../shared/global.sh"

# Use config variables with fallbacks
LAMBDA_FUNCTION_NAME="${LAMBDA_FUNCTION_NAME:-quote-gen-bloodandtreasure}"
API_GATEWAY_NAME="${API_GATEWAY_NAME:-quote-gen-bloodandtreasure-api}"
API_GATEWAY_STAGE="${API_GATEWAY_STAGE:-prod}"
AWS_REGION="${AWS_REGION:-us-east-1}"

# Note: Colors, logging functions, and get_account_id are now provided by global.sh
# (get_account_id is available as get_aws_account_id)

# Create or get API Gateway
create_api_gateway() {
    log_info "Creating/checking API Gateway..."
    
    # Check if API exists
    local api_id=$(aws apigateway get-rest-apis \
        --query "items[?name=='$API_GATEWAY_NAME'].id" \
        --output text 2>/dev/null || echo "")
    
    if [ -n "$api_id" ] && [ "$api_id" != "None" ]; then
        log_success "Found existing API Gateway: $API_GATEWAY_NAME (ID: $api_id)"
    else
        log_info "Creating new API Gateway: $API_GATEWAY_NAME"
        
        api_id=$(aws apigateway create-rest-api \
            --name "$API_GATEWAY_NAME" \
            --description "$API_GATEWAY_DESCRIPTION" \
            --endpoint-configuration types=REGIONAL \
            --query 'id' --output text)
        
        log_success "Created API Gateway: $API_GATEWAY_NAME (ID: $api_id)"
    fi
    
    echo "$api_id"
}

# Get root resource ID
get_root_resource_id() {
    local api_id=$1
    
    aws apigateway get-resources \
        --rest-api-id "$api_id" \
        --query "items[?path=='/'].id" \
        --output text
}

# Create proxy resource for Lambda integration
create_proxy_resource() {
    local api_id=$1
    local parent_id=$2
    
    log_info "Creating proxy resource..."
    
    # Check if proxy resource exists
    local proxy_resource_id=$(aws apigateway get-resources \
        --rest-api-id "$api_id" \
        --query "items[?pathPart=='{proxy+}'].id" \
        --output text 2>/dev/null || echo "")
    
    if [ -n "$proxy_resource_id" ] && [ "$proxy_resource_id" != "None" ]; then
        log_success "Found existing proxy resource"
    else
        proxy_resource_id=$(aws apigateway create-resource \
            --rest-api-id "$api_id" \
            --parent-id "$parent_id" \
            --path-part "{proxy+}" \
            --query 'id' --output text)
        
        log_success "Created proxy resource"
    fi
    
    echo "$proxy_resource_id"
}

# Create ANY method for proxy resource
create_proxy_method() {
    local api_id=$1
    local resource_id=$2
    local lambda_arn=$3
    local account_id=$4
    
    log_info "Creating ANY method for proxy resource..."
    
    # Check if method exists
    if aws apigateway get-method \
        --rest-api-id "$api_id" \
        --resource-id "$resource_id" \
        --http-method ANY &>/dev/null; then
        log_info "Method already exists, updating..."
        
        # Delete existing method to recreate it
        aws apigateway delete-method \
            --rest-api-id "$api_id" \
            --resource-id "$resource_id" \
            --http-method ANY &>/dev/null || true
    fi
    
    # Create method
    aws apigateway put-method \
        --rest-api-id "$api_id" \
        --resource-id "$resource_id" \
        --http-method ANY \
        --authorization-type NONE \
        --request-parameters method.request.path.proxy=true > /dev/null
    
    # Create integration
    aws apigateway put-integration \
        --rest-api-id "$api_id" \
        --resource-id "$resource_id" \
        --http-method ANY \
        --type AWS_PROXY \
        --integration-http-method POST \
        --uri "arn:aws:apigateway:$AWS_REGION:lambda:path/2015-03-31/functions/$lambda_arn/invocations" > /dev/null
    
    log_success "Created ANY method with Lambda proxy integration"
}

# Create root method for handling root path
create_root_method() {
    local api_id=$1
    local resource_id=$2
    local lambda_arn=$3
    
    log_info "Creating ANY method for root resource..."
    
    # Check if method exists
    if aws apigateway get-method \
        --rest-api-id "$api_id" \
        --resource-id "$resource_id" \
        --http-method ANY &>/dev/null; then
        log_info "Root method already exists, updating..."
        
        # Delete existing method to recreate it
        aws apigateway delete-method \
            --rest-api-id "$api_id" \
            --resource-id "$resource_id" \
            --http-method ANY &>/dev/null || true
    fi
    
    # Create method
    aws apigateway put-method \
        --rest-api-id "$api_id" \
        --resource-id "$resource_id" \
        --http-method ANY \
        --authorization-type NONE > /dev/null
    
    # Create integration
    aws apigateway put-integration \
        --rest-api-id "$api_id" \
        --resource-id "$resource_id" \
        --http-method ANY \
        --type AWS_PROXY \
        --integration-http-method POST \
        --uri "arn:aws:apigateway:$AWS_REGION:lambda:path/2015-03-31/functions/$lambda_arn/invocations" > /dev/null
    
    log_success "Created root ANY method with Lambda proxy integration"
}

# Add Lambda permission for API Gateway
add_lambda_permission() {
    local api_id=$1
    local account_id=$2
    
    log_info "Adding Lambda permission for API Gateway..."
    
    local statement_id="apigateway-invoke-lambda-${api_id}"
    
    # Remove existing permission if it exists
    aws lambda remove-permission \
        --function-name "$LAMBDA_FUNCTION_NAME" \
        --statement-id "$statement_id" &>/dev/null || true
    
    # Add permission
    aws lambda add-permission \
        --function-name "$LAMBDA_FUNCTION_NAME" \
        --statement-id "$statement_id" \
        --action lambda:InvokeFunction \
        --principal apigateway.amazonaws.com \
        --source-arn "arn:aws:execute-api:$AWS_REGION:$account_id:$api_id/*/*" > /dev/null
    
    log_success "Added Lambda permission for API Gateway"
}

# Enable CORS for API Gateway
enable_cors() {
    local api_id=$1
    local resource_id=$2
    
    log_info "Enabling CORS for API Gateway..."
    
    # Check if OPTIONS method exists
    if aws apigateway get-method \
        --rest-api-id "$api_id" \
        --resource-id "$resource_id" \
        --http-method OPTIONS &>/dev/null; then
        log_info "OPTIONS method already exists, updating..."
        
        # Delete existing method to recreate it
        aws apigateway delete-method \
            --rest-api-id "$api_id" \
            --resource-id "$resource_id" \
            --http-method OPTIONS &>/dev/null || true
    fi
    
    # Create OPTIONS method
    aws apigateway put-method \
        --rest-api-id "$api_id" \
        --resource-id "$resource_id" \
        --http-method OPTIONS \
        --authorization-type NONE > /dev/null
    
    # Create mock integration for OPTIONS
    aws apigateway put-integration \
        --rest-api-id "$api_id" \
        --resource-id "$resource_id" \
        --http-method OPTIONS \
        --type MOCK \
        --request-templates '{"application/json": "{\"statusCode\": 200}"}' > /dev/null
    
    # Create method response
    aws apigateway put-method-response \
        --rest-api-id "$api_id" \
        --resource-id "$resource_id" \
        --http-method OPTIONS \
        --status-code 200 \
        --response-parameters method.response.header.Access-Control-Allow-Headers=false,method.response.header.Access-Control-Allow-Methods=false,method.response.header.Access-Control-Allow-Origin=false > /dev/null
    
    # Create integration response
    aws apigateway put-integration-response \
        --rest-api-id "$api_id" \
        --resource-id "$resource_id" \
        --http-method OPTIONS \
        --status-code 200 \
        --response-parameters "{\"method.response.header.Access-Control-Allow-Headers\":\"'$CORS_HEADERS'\",\"method.response.header.Access-Control-Allow-Methods\":\"'$CORS_METHODS'\",\"method.response.header.Access-Control-Allow-Origin\":\"'*'\"}" > /dev/null
    
    log_success "CORS enabled for API Gateway"
}

# Deploy API Gateway
deploy_api() {
    local api_id=$1
    
    log_info "Deploying API Gateway to $API_GATEWAY_STAGE stage..."
    
    aws apigateway create-deployment \
        --rest-api-id "$api_id" \
        --stage-name "$API_GATEWAY_STAGE" \
        --stage-description "Production deployment" \
        --description "Deployment $(date)" > /dev/null
    
    log_success "API Gateway deployed to $API_GATEWAY_STAGE stage"
}

# Main function
main() {
    log_info "Starting API Gateway deployment..."
    log_info "API Gateway: $API_GATEWAY_NAME"
    log_info "Lambda Function: $LAMBDA_FUNCTION_NAME"
    log_info "Stage: $API_GATEWAY_STAGE"
    echo
    
    # Get account ID
    local account_id=$(get_aws_account_id)
    
    # Get Lambda function ARN
    local lambda_arn=$(aws lambda get-function \
        --function-name "$LAMBDA_FUNCTION_NAME" \
        --query 'Configuration.FunctionArn' --output text)
    
    if [ -z "$lambda_arn" ] || [ "$lambda_arn" = "None" ]; then
        log_error "Lambda function $LAMBDA_FUNCTION_NAME not found. Please deploy Lambda first."
        exit 1
    fi
    
    # Create API Gateway
    local api_id=$(create_api_gateway)
    local root_resource_id=$(get_root_resource_id "$api_id")
    
    # Create proxy resource and methods
    local proxy_resource_id=$(create_proxy_resource "$api_id" "$root_resource_id")
    create_proxy_method "$api_id" "$proxy_resource_id" "$lambda_arn" "$account_id"
    create_root_method "$api_id" "$root_resource_id" "$lambda_arn"
    
    # Enable CORS
    enable_cors "$api_id" "$proxy_resource_id"
    enable_cors "$api_id" "$root_resource_id"
    
    # Add Lambda permissions
    add_lambda_permission "$api_id" "$account_id"
    
    # Deploy API
    deploy_api "$api_id"
    
    # Get API endpoint
    local api_endpoint="https://$api_id.execute-api.$AWS_REGION.amazonaws.com/$API_GATEWAY_STAGE"
    
    echo
    log_success "API Gateway deployment completed successfully!"
    echo
    echo "=== API GATEWAY INFORMATION ==="
    echo "API Gateway ID: $api_id"
    echo "API Gateway Name: $API_GATEWAY_NAME"
    echo "Stage: $API_GATEWAY_STAGE"
    echo "Endpoint URL: $api_endpoint"
    echo "Lambda Function: $LAMBDA_FUNCTION_NAME"
    echo "Lambda ARN: $lambda_arn"
    echo
    log_info "Test your API: curl $api_endpoint/api/health"
}

# Run main function
main "$@"
