#!/bin/bash

# AWS API Gateway Documentation Script
# This script adds comprehensive documentation to the API Gateway

# Get the directory where this script is located
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"

# Source global functions and configuration
source "$SCRIPT_DIR/../shared/global.sh"

# Use config variables with fallbacks
API_GATEWAY_NAME="${API_GATEWAY_NAME:-quote-gen-bloodandtreasure-api}"
API_GATEWAY_STAGE="${API_GATEWAY_STAGE:-prod}"
AWS_REGION="${AWS_REGION:-us-east-1}"

# Get API Gateway ID
get_api_gateway_id() {
    log_info "Finding API Gateway..."
    
    local api_id=$(aws apigateway get-rest-apis \
        --query "items[?name=='$API_GATEWAY_NAME'].id" \
        --output text 2>/dev/null || echo "")
    
    if [ -z "$api_id" ] || [ "$api_id" = "None" ]; then
        log_error "API Gateway '$API_GATEWAY_NAME' not found. Please deploy the API first."
        exit 1
    fi
    
    log_success "Found API Gateway: $API_GATEWAY_NAME (ID: $api_id)"
    echo "$api_id"
}

# Add method documentation
add_method_documentation() {
    local api_id=$1
    local resource_path=$2
    local http_method=$3
    local summary=$4
    local description=$5

    log_info "Adding documentation for $http_method $resource_path..."

    # Create documentation part for method
    aws apigateway create-documentation-part \
        --rest-api-id "$api_id" \
        --location type=METHOD,path="$resource_path",method="$http_method" \
        --properties "{\"summary\":\"$summary\",\"description\":\"$description\"}" 2>/dev/null || true
}

# Add response documentation
add_response_documentation() {
    local api_id=$1
    local resource_path=$2
    local http_method=$3
    local status_code=$4
    local description=$5

    # Create documentation part for response
    aws apigateway create-documentation-part \
        --rest-api-id "$api_id" \
        --location type=RESPONSE,path="$resource_path",method="$http_method",statusCode="$status_code" \
        --properties "{\"description\":\"$description\"}" 2>/dev/null || true
}

# Create API documentation
create_api_documentation() {
    local api_id=$1
    
    log_info "Creating comprehensive API documentation..."
    
    # API Overview Documentation
    aws apigateway create-documentation-part \
        --rest-api-id "$api_id" \
        --location type=API \
        --properties '{"description":"AI Chatbot API - A comprehensive REST API for processing chat requests, image analysis, and generating project estimates. This API supports text-based conversations, image upload and analysis, and follow-up interactions with conversation context.","version":"1.0.0","title":"AI Chatbot API"}' 2>/dev/null || true
    
    # Health Check Endpoint Documentation
    add_method_documentation "$api_id" "/api/health" "GET" \
        "API Health Check" \
        "Health check endpoint to verify API availability and status. Returns a simple JSON response indicating the API is operational."

    add_response_documentation "$api_id" "/api/health" "GET" "200" \
        "API is healthy and operational. Returns status and message."
    
    # Chat Endpoint Documentation
    add_method_documentation "$api_id" "/api/chat" "POST" \
        "Process Chat Request" \
        "Process text-based chat requests with AI assistant for project estimation and consultation. Accepts an array of messages and returns AI-generated responses with optional assumptions."

    add_response_documentation "$api_id" "/api/chat" "POST" "200" \
        "Successful chat response with AI-generated content, conversation ID, and optional assumptions."

    add_response_documentation "$api_id" "/api/chat" "POST" "500" \
        "Internal server error occurred while processing the chat request."

    # Follow-up Endpoint Documentation
    add_method_documentation "$api_id" "/api/followup" "POST" \
        "Process Follow-up Request" \
        "Process follow-up chat requests within an existing conversation context. Requires a conversation ID and maintains conversation history for contextual responses."

    add_response_documentation "$api_id" "/api/followup" "POST" "200" \
        "Successful follow-up response with contextual AI-generated content."

    add_response_documentation "$api_id" "/api/followup" "POST" "500" \
        "Internal server error occurred while processing the follow-up request."

    # Upload Endpoint Documentation
    add_method_documentation "$api_id" "/api/upload" "POST" \
        "Upload and Analyze Image" \
        "Upload and analyze images for project estimation. Supports common image formats (JPEG, PNG, GIF, WebP) up to 10MB. Accepts multipart/form-data with image file and optional message."

    add_response_documentation "$api_id" "/api/upload" "POST" "200" \
        "Successful image analysis with AI-generated insights about the uploaded image."

    add_response_documentation "$api_id" "/api/upload" "POST" "400" \
        "Bad request - no image file was uploaded or invalid file format."

    add_response_documentation "$api_id" "/api/upload" "POST" "500" \
        "Internal server error occurred while processing the image upload."

    log_success "API documentation created successfully"
}

# Create documentation version
create_documentation_version() {
    local api_id=$1

    log_info "Creating documentation version..."

    local version_name="v1.0.0-$(date +%Y%m%d)"
    local description="API Documentation created on $(date)"

    aws apigateway create-documentation-version \
        --rest-api-id "$api_id" \
        --documentation-version "$version_name" \
        --description "$description" \
        --stage-name "$API_GATEWAY_STAGE" 2>/dev/null || true

    log_success "Documentation version created: $version_name"
}

# Export API documentation
export_api_documentation() {
    local api_id=$1

    log_info "Exporting API documentation..."

    # Export as OpenAPI/Swagger JSON
    local output_file="api-documentation.json"

    aws apigateway get-export \
        --rest-api-id "$api_id" \
        --stage-name "$API_GATEWAY_STAGE" \
        --export-type swagger \
        --parameters extensions=documentation \
        "$output_file" 2>/dev/null || true

    if [ -f "$output_file" ]; then
        log_success "API documentation exported to: $output_file"
        echo "  You can import this into Postman, Insomnia, or other API tools"
    else
        log_warning "Failed to export API documentation"
    fi
}

# Main function
main() {
    log_info "Adding documentation to API Gateway..."
    log_info "API Gateway: $API_GATEWAY_NAME"
    log_info "Stage: $API_GATEWAY_STAGE"
    echo

    # Get API Gateway ID
    local api_id=$(get_api_gateway_id)

    # Create comprehensive documentation
    create_api_documentation "$api_id"

    # Create documentation version
    create_documentation_version "$api_id"

    # Export documentation
    export_api_documentation "$api_id"

    # Get API endpoint
    local api_endpoint="https://$api_id.execute-api.$AWS_REGION.amazonaws.com/$API_GATEWAY_STAGE"

    echo
    log_success "API Gateway documentation added successfully!"
    echo
    echo "=== API DOCUMENTATION INFORMATION ==="
    echo "API Gateway ID: $api_id"
    echo "API Gateway Name: $API_GATEWAY_NAME"
    echo "Stage: $API_GATEWAY_STAGE"
    echo "Endpoint URL: $api_endpoint"
    echo
    echo "=== AVAILABLE ENDPOINTS ==="
    echo "GET  $api_endpoint/api/health     - Health check"
    echo "POST $api_endpoint/api/chat       - Process chat requests"
    echo "POST $api_endpoint/api/followup   - Process follow-up requests"
    echo "POST $api_endpoint/api/upload     - Upload and analyze images"
    echo
    echo "=== DOCUMENTATION ACCESS ==="
    echo "AWS Console: https://console.aws.amazon.com/apigateway/home?region=$AWS_REGION#/apis/$api_id/documentation"
    echo "Exported Documentation: ./api-documentation.json"
    echo
    log_info "You can now view the documentation in the AWS API Gateway console"
    log_info "or import the exported JSON into API testing tools like Postman"
}

# Run main function
main "$@"
