# Frontend Deployment Scripts

This directory contains scripts for deploying and managing the React frontend application to AWS.

## Scripts

### `deploy-frontend.sh`
**Full frontend deployment** - Creates complete AWS infrastructure:
- S3 bucket with static website hosting
- CloudFront distribution with Origin Access Control (OAC)
- Proper caching policies and security headers
- Builds and deploys the React application

**Usage:**
```bash
./deploy-frontend.sh
# or via npm
npm run deploy:frontend
```

### `update-frontend.sh`
**Quick frontend updates** - Updates existing deployment:
- Builds the React application
- Uploads new files to S3
- Invalidates CloudFront cache
- Much faster than full deployment

**Usage:**
```bash
./update-frontend.sh
# or via npm
npm run deploy:frontend:update
```

### `frontend-status.sh`
**Status check** - Checks the health of frontend infrastructure:
- S3 bucket configuration
- CloudFront distribution status
- Website accessibility
- Recent invalidations
- AWS costs (last 7 days)

**Usage:**
```bash
./frontend-status.sh
# or via npm
npm run deploy:frontend:status
```

## Infrastructure Created

- **S3 Bucket**: `quote-gen.bloodandtreasure.com`
  - Static website hosting enabled
  - Public access blocked (accessed via CloudFront only)
  - Optimized caching policies

- **CloudFront Distribution**
  - Origin Access Control (OAC) for secure S3 access
  - Global edge locations for fast content delivery
  - Automatic HTTPS with AWS certificate
  - Optimized caching for static assets

## Prerequisites

- AWS CLI installed and configured
- Node.js and npm installed
- Proper AWS permissions for S3 and CloudFront

## Configuration

All configuration is managed in `../shared/config.sh`. Key variables:
- `BUCKET_NAME`: S3 bucket name
- `BUILD_DIR`: Local build directory (default: `dist`)
- `CLOUDFRONT_COMMENT`: Distribution identifier
- Cache control settings for different file types
