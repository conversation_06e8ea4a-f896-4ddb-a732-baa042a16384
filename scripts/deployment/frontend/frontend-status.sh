#!/bin/bash

# AWS Frontend Deployment Status Script
# This script checks the status of your AWS infrastructure and deployment

# Get the directory where this script is located
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"

# Source global functions and configuration
source "$SCRIPT_DIR/../shared/global.sh"

# Use config variables with fallbacks
BUCKET_NAME="${BUCKET_NAME:-quote-gen.bloodandtreasure.com}"
CLOUDFRONT_COMMENT="${CLOUDFRONT_COMMENT:-Frontend Distribution for $BUCKET_NAME}"

# Note: Colors and logging functions are now provided by global.sh

# Check if AWS CLI is installed and configured
check_aws_cli() {
    if ! command -v aws &> /dev/null; then
        log_error "AWS CLI is not installed."
        return 1
    fi
    
    if ! aws sts get-caller-identity &> /dev/null; then
        log_error "AWS CLI is not configured or credentials are invalid."
        return 1
    fi
    
    local account_id=$(aws sts get-caller-identity --query Account --output text)
    local user_arn=$(aws sts get-caller-identity --query Arn --output text)
    log_success "AWS CLI configured. Account: $account_id"
    echo "           User: $user_arn"
    return 0
}

# Check S3 bucket status
check_s3_bucket() {
    log_info "Checking S3 bucket status..."
    
    if aws s3api head-bucket --bucket "$BUCKET_NAME" 2>/dev/null; then
        log_success "S3 bucket '$BUCKET_NAME' exists"
        
        # Get bucket region
        local region=$(aws s3api get-bucket-location --bucket "$BUCKET_NAME" --query 'LocationConstraint' --output text)
        if [ "$region" = "None" ]; then
            region="us-east-1"
        fi
        echo "           Region: $region"
        
        # Check if website hosting is enabled
        if aws s3api get-bucket-website --bucket "$BUCKET_NAME" &>/dev/null; then
            log_success "Static website hosting is enabled"
        else
            log_warning "Static website hosting is not enabled"
        fi
        
        # Check public access block settings
        local pab=$(aws s3api get-public-access-block --bucket "$BUCKET_NAME" --query 'PublicAccessBlockConfiguration' 2>/dev/null || echo "null")
        if [ "$pab" != "null" ]; then
            echo "           Public Access Block: Configured"
        else
            log_warning "Public Access Block: Not configured"
        fi
        
        # Get object count and size
        local objects=$(aws s3 ls "s3://$BUCKET_NAME" --recursive --summarize | tail -2)
        echo "           $objects"
        
        return 0
    else
        log_error "S3 bucket '$BUCKET_NAME' does not exist"
        return 1
    fi
}

# Check CloudFront distribution status
check_cloudfront_distribution() {
    log_info "Checking CloudFront distribution status..."
    
    local distribution_id=$(aws cloudfront list-distributions \
        --query "DistributionList.Items[?Comment=='$CLOUDFRONT_COMMENT'].Id" \
        --output text)
    
    if [ -n "$distribution_id" ] && [ "$distribution_id" != "None" ]; then
        log_success "CloudFront distribution found: $distribution_id"
        
        # Get distribution details
        local distribution_info=$(aws cloudfront get-distribution --id "$distribution_id")
        local domain_name=$(echo "$distribution_info" | jq -r '.Distribution.DomainName')
        local status=$(echo "$distribution_info" | jq -r '.Distribution.Status')
        local enabled=$(echo "$distribution_info" | jq -r '.Distribution.DistributionConfig.Enabled')
        
        echo "           Domain: $domain_name"
        echo "           Status: $status"
        echo "           Enabled: $enabled"
        echo "           URL: https://$domain_name"
        
        # Check for recent invalidations
        log_info "Checking recent invalidations..."
        local invalidations=$(aws cloudfront list-invalidations --distribution-id "$distribution_id" \
            --query 'InvalidationList.Items[0:3].[Id,Status,CreateTime]' --output table 2>/dev/null || echo "No invalidations found")
        echo "$invalidations"
        
        return 0
    else
        log_error "CloudFront distribution not found"
        return 1
    fi
}

# Check deployment health
check_deployment_health() {
    log_info "Checking deployment health..."
    
    # Get CloudFront domain
    local distribution_id=$(aws cloudfront list-distributions \
        --query "DistributionList.Items[?Comment=='$CLOUDFRONT_COMMENT'].Id" \
        --output text)
    
    if [ -n "$distribution_id" ] && [ "$distribution_id" != "None" ]; then
        local domain_name=$(aws cloudfront get-distribution --id "$distribution_id" \
            --query 'Distribution.DomainName' --output text)
        
        # Test HTTP response
        log_info "Testing HTTPS endpoint..."
        local http_status=$(curl -s -o /dev/null -w "%{http_code}" "https://$domain_name" || echo "000")
        
        if [ "$http_status" = "200" ]; then
            log_success "Website is responding (HTTP $http_status)"
        elif [ "$http_status" = "000" ]; then
            log_error "Website is not reachable"
        else
            log_warning "Website returned HTTP $http_status"
        fi
        
        # Test if it's serving the React app
        local content=$(curl -s "https://$domain_name" | head -20)
        if echo "$content" | grep -q "<!doctype html" || echo "$content" | grep -q "<!DOCTYPE html"; then
            log_success "Serving HTML content"
        else
            log_warning "Not serving expected HTML content"
        fi
    fi
}

# Show recent AWS costs (optional)
show_recent_costs() {
    log_info "Checking recent AWS costs (last 7 days)..."
    
    local end_date=$(date +%Y-%m-%d)
    local start_date=$(date -d '7 days ago' +%Y-%m-%d)
    
    # This requires Cost Explorer API access
    local costs=$(aws ce get-cost-and-usage \
        --time-period Start="$start_date",End="$end_date" \
        --granularity DAILY \
        --metrics BlendedCost \
        --group-by Type=DIMENSION,Key=SERVICE \
        --query 'ResultsByTime[*].Groups[?Keys[0]==`Amazon Simple Storage Service` || Keys[0]==`Amazon CloudFront`].[Keys[0],Metrics.BlendedCost.Amount]' \
        --output table 2>/dev/null || echo "Cost information not available (requires Cost Explorer API access)")
    
    echo "$costs"
}

# Main status check function
main() {
    echo "=== AWS FRONTEND DEPLOYMENT STATUS ==="
    echo "Bucket: $BUCKET_NAME"
    echo "Timestamp: $(date)"
    echo

    local overall_status=0

    # Check AWS CLI
    if ! check_aws_cli; then
        overall_status=1
    fi
    echo

    # Check S3 bucket
    if ! check_s3_bucket; then
        overall_status=1
    fi
    echo

    # Check CloudFront distribution
    if ! check_cloudfront_distribution; then
        overall_status=1
    fi
    echo

    # Check deployment health
    check_deployment_health
    echo

    # Show costs (optional, comment out if not needed)
    # show_recent_costs
    # echo

    if [ $overall_status -eq 0 ]; then
        log_success "All infrastructure components are healthy!"
    else
        log_warning "Some infrastructure components need attention."
        echo
        log_info "To create missing infrastructure, run: ./scripts/deploy.sh"
        log_info "To update existing deployment, run: ./scripts/update-deployment.sh"
    fi

    exit $overall_status
}

# Run main function
main "$@"
