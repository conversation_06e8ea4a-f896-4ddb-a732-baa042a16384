# Backend Connection Scripts

⚠️  **MOVED**: Connection scripts have been moved to `scripts/connect/backend/`

This directory contains backward compatibility wrappers that redirect to the new location.

## New Location

All connection scripts are now located in:
- `scripts/connect/backend/connect-backend.sh`
- `scripts/connect/backend/test-backend-connection.js`
- `scripts/connect/backend/update-lambda-env.sh`

The scripts in this directory will automatically redirect to the new location.

## Quick Start

### Check Connection Status
```bash
# Check if local backend is running
npm run connect:local

# Check deployed backend status  
npm run connect:deployed

# Show all connection options
npm run connect:backend
```

### Start Local Backend
```bash
# Start local backend server
npm run connect:start

# Or use the full development environment
npm run dev
```

### Test Backend Connection
```bash
# Test local backend
npm run test:backend

# Test custom URL
node scripts/test-backend-connection.js test https://your-api-url.com
```

## Available Scripts

### 1. `connect-backend.sh`
Main connection script with multiple commands:

- `./scripts/connect-backend.sh help` - Show help and options
- `./scripts/connect-backend.sh start` - Start local backend server
- `./scripts/connect-backend.sh local` - Check local backend status
- `./scripts/connect-backend.sh deployed` - Check deployed backend status
- `./scripts/connect-backend.sh test <url>` - Test custom backend URL

### 2. `test-backend-connection.js`
Comprehensive backend testing script:

- Tests health endpoint (`/api/health`)
- Tests chat endpoint (`/api/chat`) 
- Gathers backend information
- Provides detailed connection diagnostics

## NPM Scripts

Add these to your workflow:

```bash
# Connection management
npm run connect:backend      # Show connection options
npm run connect:local        # Check local backend
npm run connect:deployed     # Check deployed backend  
npm run connect:start        # Start local backend

# Testing
npm run test:backend         # Test backend connection

# Development
npm run dev                  # Start both frontend & backend
npm run dev:backend          # Backend only
npm run dev:frontend         # Frontend only
```

## Backend Environments

### Local Development
- **URL**: `http://localhost:3001`
- **Port**: 3001 (configurable in `backend/.env`)
- **Start**: `npm run connect:start` or `npm run dev:backend`
- **Health Check**: `http://localhost:3001/api/health`

### Deployed Backend
- **Platform**: AWS Lambda + API Gateway
- **Check Status**: `npm run connect:deployed`
- **Deploy**: `npm run deploy:backend`
- **Update**: `npm run deploy:backend:update`

## Troubleshooting

### Local Backend Not Starting
1. Check if dependencies are installed: `cd backend && npm install`
2. Verify `.env` file exists in `backend/` directory
3. Check if port 3001 is available
4. Review backend logs for errors

### Connection Test Failures
1. Ensure backend is running: `npm run connect:local`
2. Check firewall/network settings
3. Verify API endpoints are responding
4. Check backend logs for errors

### Deployed Backend Issues
1. Check AWS credentials: `aws configure list`
2. Verify deployment status: `npm run connect:deployed`
3. Check CloudWatch logs for errors
4. Ensure API Gateway is properly configured

## Configuration

### Local Backend
Edit `backend/.env`:
```env
PORT=3001
OPENAI_API_KEY=your-api-key
```

### Deployed Backend
Configuration is managed through deployment scripts in `scripts/deployment/backend/`.

## Examples

### Basic Connection Test
```bash
# Test local backend
npm run test:backend

# Expected output:
# ✅ Health check passed (HTTP 200)
# ✅ Chat endpoint is functional
# ✅ Backend connection test completed successfully!
```

### Start Local Development
```bash
# Start backend only
npm run connect:start

# Start full development environment
npm run dev
```

### Check Deployment Status
```bash
# Check deployed backend
npm run connect:deployed

# Expected output shows Lambda function, API Gateway, and endpoint status
```

## Integration

These scripts integrate with the existing project structure:

- Uses existing backend configuration from `backend/.env`
- Leverages deployment scripts in `scripts/deployment/`
- Compatible with existing NPM scripts in `package.json`
- Follows project logging and error handling patterns

## Support

For issues with these connection scripts:

1. Check the script output for specific error messages
2. Verify your environment setup (Node.js, AWS CLI, etc.)
3. Review the backend logs for additional context
4. Ensure all dependencies are installed

The scripts provide detailed logging to help diagnose connection issues.
