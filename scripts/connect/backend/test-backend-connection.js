#!/usr/bin/env node

/**
 * Backend Connection Test Script
 * Tests connectivity to the AI Chatbot backend API
 */

import { createRequire } from 'module';
const require = createRequire(import.meta.url);

// Configuration
const LOCAL_URL = 'http://localhost:3001';
const HEALTH_ENDPOINT = '/api/health';
const CHAT_ENDPOINT = '/api/chat';

// Colors for console output
const colors = {
    reset: '\x1b[0m',
    red: '\x1b[31m',
    green: '\x1b[32m',
    yellow: '\x1b[33m',
    blue: '\x1b[34m',
    cyan: '\x1b[36m'
};

// Logging functions (matching global.sh style)
const log = {
    info: (msg) => console.log(`${colors.blue}[INFO]${colors.reset} ${msg}`),
    success: (msg) => console.log(`${colors.green}[SUCCESS]${colors.reset} ${msg}`),
    warning: (msg) => console.log(`${colors.yellow}[WARNING]${colors.reset} ${msg}`),
    error: (msg) => console.log(`${colors.red}[ERROR]${colors.reset} ${msg}`),
    data: (msg) => console.log(`${colors.cyan}[DATA]${colors.reset} ${msg}`)
};

// HTTP request function (using Node.js built-in modules)
async function makeRequest(url, options = {}) {
    const { default: fetch } = await import('node-fetch').catch(() => {
        // Fallback to built-in fetch in Node.js 18+
        return { default: globalThis.fetch };
    });
    
    if (!fetch) {
        throw new Error('Fetch not available. Please use Node.js 18+ or install node-fetch');
    }
    
    const defaultOptions = {
        method: 'GET',
        headers: {
            'Content-Type': 'application/json',
            'User-Agent': 'Backend-Connection-Test/1.0'
        },
        timeout: 10000
    };
    
    const mergedOptions = { ...defaultOptions, ...options };
    
    try {
        const response = await fetch(url, mergedOptions);
        const data = await response.text();
        
        return {
            status: response.status,
            statusText: response.statusText,
            data: data,
            headers: Object.fromEntries(response.headers.entries())
        };
    } catch (error) {
        throw new Error(`Request failed: ${error.message}`);
    }
}

// Test health endpoint
async function testHealthEndpoint(baseUrl) {
    const url = `${baseUrl}${HEALTH_ENDPOINT}`;
    log.info(`Testing health endpoint: ${url}`);
    
    try {
        const response = await makeRequest(url);
        
        if (response.status === 200) {
            log.success(`Health check passed (HTTP ${response.status})`);
            log.data(`Response: ${response.data}`);
            
            try {
                const jsonData = JSON.parse(response.data);
                if (jsonData.status === 'healthy') {
                    log.success('Backend reports healthy status');
                    return true;
                } else {
                    log.warning(`Backend status: ${jsonData.status}`);
                    return false;
                }
            } catch (e) {
                log.warning('Response is not valid JSON');
                return response.status === 200;
            }
        } else {
            log.error(`Health check failed (HTTP ${response.status})`);
            log.data(`Response: ${response.data}`);
            return false;
        }
    } catch (error) {
        log.error(`Health check failed: ${error.message}`);
        return false;
    }
}

// Test chat endpoint
async function testChatEndpoint(baseUrl) {
    const url = `${baseUrl}${CHAT_ENDPOINT}`;
    log.info(`Testing chat endpoint: ${url}`);
    
    const testMessage = {
        message: "Hello, this is a connection test",
        conversationId: "test-connection-" + Date.now()
    };
    
    try {
        const response = await makeRequest(url, {
            method: 'POST',
            body: JSON.stringify(testMessage)
        });
        
        if (response.status === 200) {
            log.success(`Chat endpoint responded (HTTP ${response.status})`);
            
            try {
                const jsonData = JSON.parse(response.data);
                if (jsonData.response) {
                    log.success('Chat endpoint is functional');
                    log.data(`AI Response: ${jsonData.response.substring(0, 100)}...`);
                    return true;
                } else {
                    log.warning('Chat response format unexpected');
                    return false;
                }
            } catch (e) {
                log.warning('Chat response is not valid JSON');
                return false;
            }
        } else {
            log.error(`Chat endpoint failed (HTTP ${response.status})`);
            log.data(`Response: ${response.data}`);
            return false;
        }
    } catch (error) {
        log.error(`Chat endpoint test failed: ${error.message}`);
        return false;
    }
}

// Get backend info
async function getBackendInfo(baseUrl) {
    log.info('Gathering backend information...');
    
    // Try to get package info or version
    const endpoints = [
        '/api/version',
        '/api/info',
        '/package.json'
    ];
    
    for (const endpoint of endpoints) {
        try {
            const response = await makeRequest(`${baseUrl}${endpoint}`);
            if (response.status === 200) {
                log.success(`Found info endpoint: ${endpoint}`);
                log.data(`Info: ${response.data}`);
                return;
            }
        } catch (error) {
            // Continue to next endpoint
        }
    }
    
    log.info('No additional backend info endpoints found');
}

// Main test function
async function testBackendConnection(baseUrl = LOCAL_URL) {
    console.log('🤖 AI Chatbot Backend Connection Test');
    console.log('=====================================');
    console.log(`Testing backend at: ${baseUrl}`);
    console.log();
    
    let healthPassed = false;
    let chatPassed = false;
    
    try {
        // Test health endpoint
        healthPassed = await testHealthEndpoint(baseUrl);
        console.log();
        
        // Test chat endpoint (only if health passed)
        if (healthPassed) {
            chatPassed = await testChatEndpoint(baseUrl);
            console.log();
            
            // Get additional backend info
            await getBackendInfo(baseUrl);
            console.log();
        }
        
        // Summary
        console.log('Test Summary:');
        console.log(`  Health Endpoint: ${healthPassed ? '✅ PASS' : '❌ FAIL'}`);
        console.log(`  Chat Endpoint: ${chatPassed ? '✅ PASS' : '❌ FAIL'}`);
        console.log();
        
        if (healthPassed && chatPassed) {
            log.success('Backend connection test completed successfully!');
            log.info('Your backend is ready for use.');
            return true;
        } else {
            log.warning('Backend connection test completed with issues.');
            if (!healthPassed) {
                log.info('Try starting the backend with: npm run dev:backend');
            }
            return false;
        }
        
    } catch (error) {
        log.error(`Test failed with error: ${error.message}`);
        return false;
    }
}

// CLI handling
async function main() {
    const args = process.argv.slice(2);
    const command = args[0] || 'test';
    
    switch (command) {
        case 'test':
            const url = args[1] || LOCAL_URL;
            const success = await testBackendConnection(url);
            process.exit(success ? 0 : 1);
            break;
            
        case 'help':
        default:
            console.log('Backend Connection Test Script');
            console.log('');
            console.log('Usage:');
            console.log('  node test-backend-connection.js [command] [url]');
            console.log('');
            console.log('Commands:');
            console.log('  test [url]  - Test backend connection (default: http://localhost:3001)');
            console.log('  help        - Show this help message');
            console.log('');
            console.log('Examples:');
            console.log('  node test-backend-connection.js');
            console.log('  node test-backend-connection.js test http://localhost:3001');
            console.log('  node test-backend-connection.js test https://api.example.com');
            break;
    }
}

// Run if called directly
if (import.meta.url === `file://${process.argv[1]}`) {
    main().catch(error => {
        log.error(`Script failed: ${error.message}`);
        process.exit(1);
    });
}
