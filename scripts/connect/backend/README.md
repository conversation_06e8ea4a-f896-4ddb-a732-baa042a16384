# Backend Connection Scripts

This directory contains scripts to help you connect to and test the AI Chatbot backend in different environments.

## Scripts Overview

### 1. `connect-backend.sh`
Main connection script with multiple commands for managing backend connections.

**Usage:**
```bash
./scripts/connect/backend/connect-backend.sh <command>
```

**Commands:**
- `start` - Start local backend server
- `local` - Check local backend status
- `deployed` - Check deployed backend status
- `test <url>` - Test custom backend URL
- `help` - Show help message

### 2. `test-backend-connection.js`
Comprehensive Node.js testing script that validates backend functionality.

**Usage:**
```bash
node scripts/connect/backend/test-backend-connection.js [command] [url]
```

**Features:**
- Tests health endpoint (`/api/health`)
- Tests chat endpoint (`/api/chat`)
- Gathers backend information
- Provides detailed connection diagnostics

### 3. `update-lambda-env.sh`
Updates AWS Lambda environment variables from your local `.env` file.

**Usage:**
```bash
./scripts/connect/backend/update-lambda-env.sh
```

**Features:**
- Reads environment variables from `backend/.env`
- Updates Lambda function configuration
- Tests updated function
- Uses global deployment functions

## Quick Start

### Check Connection Status
```bash
# Check if local backend is running
npm run connect:local

# Check deployed backend status  
npm run connect:deployed

# Show all connection options
npm run connect:backend
```

### Start Local Backend
```bash
# Start local backend server
npm run connect:start

# Or use the full development environment
npm run dev
```

### Test Backend Connection
```bash
# Test local backend
npm run test:backend

# Test custom URL
node scripts/connect/backend/test-backend-connection.js test https://your-api-url.com
```

### Update Lambda Environment Variables
```bash
# Update deployed Lambda with local .env variables
npm run update:lambda-env
```

## Integration with Global Functions

These scripts now use the shared global functions from `scripts/deployment/shared/global.sh`:

- **Logging**: Consistent `log_info()`, `log_success()`, `log_warning()`, `log_error()` functions
- **AWS Utilities**: `check_aws_cli()`, `resource_exists()`, `wait_for_resource()`
- **Configuration**: Shared environment variables and project paths
- **Error Handling**: Standardized error handling and cleanup

## Configuration

### Environment Variables
The scripts use these configuration variables with fallbacks:

```bash
LAMBDA_FUNCTION_NAME="${LAMBDA_FUNCTION_NAME:-quote-gen-bloodandtreasure}"
API_GATEWAY_NAME="${API_GATEWAY_NAME:-quote-gen-bloodandtreasure-api}"
API_GATEWAY_STAGE="${API_GATEWAY_STAGE:-prod}"
AWS_REGION="${AWS_REGION:-us-east-1}"
BACKEND_DIR="${BACKEND_DIR:-backend}"
```

### Local Backend
Edit `backend/.env`:
```env
PORT=3001
OPENAI_API_KEY=your-api-key
```

## Backend Environments

### Local Development
- **URL**: `http://localhost:3001`
- **Port**: 3001 (configurable in `backend/.env`)
- **Start**: `npm run connect:start` or `npm run dev:backend`
- **Health Check**: `http://localhost:3001/api/health`

### Deployed Backend
- **Platform**: AWS Lambda + API Gateway
- **Check Status**: `npm run connect:deployed`
- **Deploy**: `npm run deploy:backend`
- **Update**: `npm run deploy:backend:update`
- **Update Env**: `npm run update:lambda-env`

## NPM Scripts

The following NPM scripts are available in the root `package.json`:

```bash
# Connection management
npm run connect:backend      # Show connection options
npm run connect:local        # Check local backend
npm run connect:deployed     # Check deployed backend  
npm run connect:start        # Start local backend

# Testing
npm run test:backend         # Test backend connection

# Lambda environment management
npm run update:lambda-env    # Update Lambda environment variables
```

## Troubleshooting

### Local Backend Not Starting
1. Check if dependencies are installed: `cd backend && npm install`
2. Verify `.env` file exists in `backend/` directory
3. Check if port 3001 is available
4. Review backend logs for errors

### Connection Test Failures
1. Ensure backend is running: `npm run connect:local`
2. Check firewall/network settings
3. Verify API endpoints are responding
4. Check backend logs for errors

### Deployed Backend Issues
1. Check AWS credentials: `aws configure list`
2. Verify deployment status: `npm run connect:deployed`
3. Update environment variables: `npm run update:lambda-env`
4. Check CloudWatch logs for errors
5. Ensure API Gateway is properly configured

### Lambda Environment Variables Missing
If you get "OPENAI_API_KEY environment variable is required" error:

1. Run `npm run update:lambda-env` to sync your local `.env` file
2. Confirm the update: `npm run connect:deployed`
3. Test the endpoint: `npm run test:backend`

## Examples

### Basic Connection Test
```bash
# Test local backend
npm run test:backend

# Expected output:
# [SUCCESS] Health check passed (HTTP 200)
# [SUCCESS] Chat endpoint is functional
# [SUCCESS] Backend connection test completed successfully!
```

### Update Lambda Environment Variables
```bash
# Update Lambda with local .env variables
npm run update:lambda-env

# Follow the prompts to confirm the update
```

### Check Deployment Status
```bash
# Check deployed backend
npm run connect:deployed

# Expected output shows Lambda function, API Gateway, and endpoint status
```

## Backward Compatibility

The original scripts in the root `scripts/` directory are now backward compatibility wrappers that automatically redirect to the new location. This ensures existing workflows continue to work while encouraging migration to the new structure.

## Support

For issues with these connection scripts:

1. Check the script output for specific error messages
2. Verify your environment setup (Node.js, AWS CLI, etc.)
3. Review the backend logs for additional context
4. Ensure all dependencies are installed
5. Use the global logging functions for consistent output formatting
