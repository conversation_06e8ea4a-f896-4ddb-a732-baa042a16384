#!/bin/bash

# Backward compatibility wrapper for connect-backend.sh
# This script redirects to the new location

echo "⚠️  This script has moved to scripts/connect/backend/connect-backend.sh"
echo "🔄 Redirecting to new location..."
echo

# Get the directory where this script is located
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"

# Execute the script in the new location
exec "$SCRIPT_DIR/connect/backend/connect-backend.sh" "$@"

# Configuration
LOCAL_PORT=3001
LOCAL_URL="http://localhost:$LOCAL_PORT"
HEALTH_ENDPOINT="/api/health"

# Get the directory where this script is located
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(cd "$SCRIPT_DIR/.." && pwd)"

# Function to check if local backend is running
check_local_backend() {
    log_info "Checking local backend at $LOCAL_URL..."
    
    if command -v curl &> /dev/null; then
        local response=$(curl -s -w "%{http_code}" -o /tmp/local_backend_response.txt "$LOCAL_URL$HEALTH_ENDPOINT" 2>/dev/null || echo "000")
        
        if [ "$response" = "200" ]; then
            log_success "Local backend is running and healthy"
            echo "  URL: $LOCAL_URL"
            echo "  Health: $LOCAL_URL$HEALTH_ENDPOINT"
            echo "  Response: $(cat /tmp/local_backend_response.txt 2>/dev/null || echo 'No response body')"
            rm -f /tmp/local_backend_response.txt
            return 0
        else
            log_warning "Local backend not responding (HTTP $response)"
            rm -f /tmp/local_backend_response.txt
            return 1
        fi
    else
        log_warning "curl not available for testing local backend"
        return 1
    fi
}

# Function to start local backend
start_local_backend() {
    log_info "Starting local backend..."
    
    # Check if we're in the right directory
    if [ ! -f "$PROJECT_ROOT/backend/package.json" ]; then
        log_error "Backend directory not found. Make sure you're in the project root."
        return 1
    fi
    
    # Check if dependencies are installed
    if [ ! -d "$PROJECT_ROOT/backend/node_modules" ]; then
        log_info "Installing backend dependencies..."
        cd "$PROJECT_ROOT/backend" && npm install
        if [ $? -ne 0 ]; then
            log_error "Failed to install backend dependencies"
            return 1
        fi
    fi
    
    # Start the backend in development mode
    log_info "Starting backend in development mode..."
    echo "  Port: $LOCAL_PORT"
    echo "  Environment: development"
    echo "  Press Ctrl+C to stop"
    echo
    
    cd "$PROJECT_ROOT/backend" && npm run dev
}

# Function to check deployed backend status
check_deployed_backend() {
    log_info "Checking deployed backend status..."
    
    # Check if AWS credentials are set
    if ! command -v aws &> /dev/null; then
        log_error "AWS CLI not found. Please install AWS CLI to check deployed backend."
        return 1
    fi
    
    # Run the backend status script
    if [ -f "$SCRIPT_DIR/deployment/backend/backend-status.sh" ]; then
        "$SCRIPT_DIR/deployment/backend/backend-status.sh"
    else
        log_error "Backend status script not found"
        return 1
    fi
}

# Function to show connection options
show_connection_options() {
    echo
    log_info "Backend Connection Options:"
    echo
    echo "1. 🏠 Local Development"
    echo "   - Start local backend: $0 start"
    echo "   - Check local status: $0 local"
    echo "   - URL: $LOCAL_URL"
    echo
    echo "2. ☁️  Deployed Backend"
    echo "   - Check deployed status: $0 deployed"
    echo "   - Deploy backend: npm run deploy:backend"
    echo "   - Update backend: npm run deploy:backend:update"
    echo
    echo "3. 🔄 Full Development"
    echo "   - Start both frontend & backend: npm run dev"
    echo "   - Frontend only: npm run dev:frontend"
    echo "   - Backend only: npm run dev:backend"
    echo
}

# Function to test backend connection
test_backend_connection() {
    local url=$1
    local name=$2
    
    if [ -z "$url" ]; then
        log_error "No URL provided for testing"
        return 1
    fi
    
    log_info "Testing $name connection..."
    echo "  URL: $url"
    
    if command -v curl &> /dev/null; then
        local health_url="$url$HEALTH_ENDPOINT"
        echo "  Testing: $health_url"
        
        local response=$(curl -s -w "%{http_code}" -o /tmp/backend_test_response.txt "$health_url" 2>/dev/null || echo "000")
        
        if [ "$response" = "200" ]; then
            log_success "$name is healthy (HTTP $response)"
            echo "  Response: $(cat /tmp/backend_test_response.txt 2>/dev/null || echo 'No response body')"
        elif [ "$response" = "000" ]; then
            log_error "$name connection failed"
        else
            log_warning "$name returned HTTP $response"
            echo "  Response: $(cat /tmp/backend_test_response.txt 2>/dev/null || echo 'No response body')"
        fi
        
        rm -f /tmp/backend_test_response.txt
    else
        log_warning "curl not available for testing"
        echo "Manual test: curl $health_url"
    fi
}

# Main function
main() {
    local command=${1:-help}
    
    case $command in
        "start")
            start_local_backend
            ;;
        "local")
            check_local_backend
            ;;
        "deployed")
            check_deployed_backend
            ;;
        "test")
            local url=$2
            if [ -z "$url" ]; then
                log_error "Usage: $0 test <url>"
                echo "Example: $0 test http://localhost:3001"
                exit 1
            fi
            test_backend_connection "$url" "Custom Backend"
            ;;
        "help"|*)
            echo "🤖 AI Chatbot Backend Connection Script"
            echo
            echo "Usage: $0 <command>"
            echo
            echo "Commands:"
            echo "  start     - Start local backend server"
            echo "  local     - Check local backend status"
            echo "  deployed  - Check deployed backend status"
            echo "  test <url> - Test custom backend URL"
            echo "  help      - Show this help message"
            
            show_connection_options
            ;;
    esac
}

# Run main function with all arguments
main "$@"
