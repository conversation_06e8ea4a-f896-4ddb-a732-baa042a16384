#!/bin/bash

# Backward compatibility wrapper for update-frontend.sh
# This script redirects to the new location

echo "⚠️  This script has moved to scripts/deployment/frontend/update-frontend.sh"
echo "🔄 Redirecting to new location..."
echo

# Get the directory where this script is located
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"

# Execute the script in the new location
exec "$SCRIPT_DIR/deployment/frontend/update-frontend.sh" "$@"
