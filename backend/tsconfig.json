{"compilerOptions": {"target": "ES2020", "module": "ESNext", "moduleResolution": "Node", "outDir": "dist", "rootDir": ".", "esModuleInterop": true, "allowSyntheticDefaultImports": true, "forceConsistentCasingInFileNames": true, "strict": false, "skipLibCheck": true, "resolveJsonModule": true, "declaration": false, "noEmitOnError": false, "noImplicitAny": false, "allowJs": true}, "include": ["server.ts", "src/**/*", "services/**/*", "routes/**/*", "middleware/**/*", "prompts/**/*"], "exclude": ["node_modules", "dist"]}