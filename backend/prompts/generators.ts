import { BASE_RULES, COMPREHENSIVE_RESPONSE_INDICATORS, VAGUE_REQUEST_PROMPT, ASSUMPTION_BASED_PROMPT, COMPREHENSIVE_SOLUTION_PROMPT, CONTINUE_QUESTIONING_PROMPT, CONTINUE_ASSUMPTIONS_PROMPT, END_PROMPT_SYSTEM_MESSAGE } from './base.js';

export interface ConversationState {
  isVague: boolean;
  hasEnoughInfo: boolean;
  questionCount: number;
  hasComprehensiveResponse: boolean;
  userFrustration: boolean;
  shouldShowEndPrompt: boolean;
  shouldAutoGenerateQuote: boolean;
  userResponseCount: number;
  assumptionMode: boolean;
}

// Helper function to detect comprehensive affirmative responses
export function detectComprehensiveResponse(messages: any[]): boolean {
  const userMessages = messages.filter(msg => msg.role === 'user');
  const lastUserMessage = userMessages[userMessages.length - 1]?.content?.toLowerCase() || '';
  
  // Check for comprehensive response indicators
  const hasComprehensiveIndicator = COMPREHENSIVE_RESPONSE_INDICATORS.some(indicator => 
    lastUserMessage.includes(indicator.toLowerCase())
  );
  
  // Check for simple affirmative responses to feature lists
  const isSimpleAffirmative = /^(yes|yeah|yep|sure|ok|okay|sounds good|that works|perfect|exactly)\.?\s*$/i.test(lastUserMessage.trim());
  
  // Check if previous assistant message contained feature examples/options
  const assistantMessages = messages.filter(msg => msg.role === 'assistant');
  const lastAssistantMessage = assistantMessages[assistantMessages.length - 1]?.content || '';
  const containsFeatureExamples = /\b(feature|example|would you prioritize|include|integration|database|payment|auth|login|dashboard)\b/i.test(lastAssistantMessage);
  
  return hasComprehensiveIndicator || (isSimpleAffirmative && containsFeatureExamples);
}

// Helper function to detect user frustration with excessive questioning
export function detectUserFrustration(messages: any[]): boolean {
  const userMessages = messages.filter(msg => msg.role === 'user');
  const lastUserMessage = userMessages[userMessages.length - 1]?.content?.toLowerCase() || '';
  
  const frustrationIndicators = [
    'just tell me',
    'stop asking',
    'enough questions',
    'just give me',
    'can you just',
    'please just',
    'i already said',
    'i told you',
    'pain in the ass',
    'annoying',
    'too many questions'
  ];
  
  return frustrationIndicators.some(indicator => 
    lastUserMessage.includes(indicator)
  );
}

// Enhanced conversation state analysis
export function analyzeConversationState(messages: any[], assumptionMode: boolean = false): ConversationState {
  const userMessages = messages.filter(msg => msg.role === 'user');
  const assistantMessages = messages.filter(msg => msg.role === 'assistant');
  
  // Count questions asked by assistant (look for Q: format specifically)
  const questionCount = assistantMessages.filter(msg => 
    msg.content && msg.content.includes('Q:')
  ).length;
  
  // Count user responses (excluding the initial message)
  const userResponseCount = userMessages.length;
  
  // After exactly 3 questions, show end prompt and generate the best quote
  // We need 3 questions asked + 3 user responses to those questions + 1 initial message = 4 total user messages
  const shouldShowEndPrompt = questionCount >= 3 && userResponseCount >= 4;
  
  // Auto-generate quote exactly after 3 questions have been asked and answered
  const shouldAutoGenerateQuote = questionCount >= 3 && userResponseCount >= 4;
  
  // Check if initial request is vague (short, general terms)
  const firstUserMessage = userMessages[0]?.content || '';
  const isVague = firstUserMessage.length < 50 ||
    /\b(app|website|system|platform|tool|bot|service|generator|builder|creator)\b/i.test(firstUserMessage) &&
    !/\b(feature|function|user|data|integration|api|database|payment|auth|login|dashboard)\b/i.test(firstUserMessage);
  
  // Check if we have enough detailed information
  const totalUserContent = userMessages.map(msg => msg.content).join(' ');
  const hasEnoughInfo = totalUserContent.length > 200 && 
    /\b(feature|function|user|data|integration|api|database|payment|auth|login|dashboard|workflow|process)\b/i.test(totalUserContent) &&
    questionCount >= 2;
  
  // Check for comprehensive response
  const hasComprehensiveResponse = detectComprehensiveResponse(messages);
  
  // Check for user frustration
  const userFrustration = detectUserFrustration(messages);
  
  return { 
    isVague, 
    hasEnoughInfo: hasEnoughInfo || hasComprehensiveResponse || shouldShowEndPrompt, 
    questionCount, 
    hasComprehensiveResponse,
    userFrustration,
    shouldShowEndPrompt,
    shouldAutoGenerateQuote,
    userResponseCount,
    assumptionMode
  };
}

// Helper function to get assumption guidance based on conversation history
export function getAssumptionGuidance(messages: any[], questionCount: number): string {
  const assistantMessages = messages.filter(msg => msg.role === 'assistant');
  const previousAssumptions = assistantMessages
    .map(msg => msg.content)
    .filter(content => content && content.includes('Assume:'))
    .map(content => {
      const match = content.match(/Assume:\s*([^\n\r]*)/);
      return match ? match[1].trim() : '';
    })
    .filter(assumption => assumption.length > 0);

  // Define different assumption categories to cycle through
  const assumptionCategories = [
    'target users and user experience (e.g., business professionals, general public, specific industry)',
    'core features and functionality (e.g., user management, data processing, communication tools)', 
    'technology stack and architecture (e.g., web-based, mobile-first, cloud deployment)',
    'scale and performance requirements (e.g., number of users, data volume, response time)',
    'integrations and third-party services (e.g., payment systems, APIs, external databases)',
    'security and compliance needs (e.g., data protection, user authentication, industry standards)'
  ];

  // Get the category for this question based on count
  const currentCategory = assumptionCategories[questionCount % assumptionCategories.length];
  
  let guidance = `IMPORTANT: Change your assumption focus for this question. ${currentCategory}.`;
  
  if (previousAssumptions.length > 0) {
    guidance += ` NEVER repeat these previous assumptions: "${previousAssumptions.join('", "')}". Make your new assumption completely different and focused on a new aspect.`;
  }
  
  return guidance;
}

// Generate context-aware system prompt
export function generateSystemPrompt(format: string, conversationState: ConversationState, messages: any[] = []): string {
  const basePrompt = `${BASE_RULES}

    Format: ${format || 'web'} application`;

  // If we've completed 3 Q&A cycles, show end prompt and auto-generate quote
  if (conversationState.shouldShowEndPrompt || conversationState.shouldAutoGenerateQuote) {
    return `${basePrompt}

${END_PROMPT_SYSTEM_MESSAGE}`;
  }

  // If user indicated comprehensive response or is frustrated, provide full solution
  if (conversationState.hasComprehensiveResponse || conversationState.userFrustration) {
    return `${basePrompt}

${COMPREHENSIVE_SOLUTION_PROMPT}`;
  }

  // If we have enough info, provide comprehensive response
  if (conversationState.hasEnoughInfo) {
    return `${basePrompt}

${COMPREHENSIVE_SOLUTION_PROMPT}`;
  }

  // If initial vague request, use assumption mode or questioning mode
  if (conversationState.isVague && conversationState.questionCount === 0) {
    if (conversationState.assumptionMode) {
      return `${basePrompt}

${ASSUMPTION_BASED_PROMPT}

${getAssumptionGuidance(messages, conversationState.questionCount)}`;
    } else {
      return `${basePrompt}

${VAGUE_REQUEST_PROMPT}

${getAssumptionGuidance(messages, conversationState.questionCount)}`;
    }
  }

  // Continue gathering information based on mode
  if (conversationState.assumptionMode) {
    return `${basePrompt}

${CONTINUE_ASSUMPTIONS_PROMPT}

${getAssumptionGuidance(messages, conversationState.questionCount)}`;
  } else {
    return `${basePrompt}

${CONTINUE_QUESTIONING_PROMPT}

${getAssumptionGuidance(messages, conversationState.questionCount)}`;
  }
}
