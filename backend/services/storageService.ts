import fs from 'fs';
import fsPromises from 'fs/promises';
import path from 'path';

export class StorageService {
  static async saveChatSession(messages: any[], format: string | undefined): Promise<void> {
    try {
      // In Lambda environment, use /tmp directory which is writable
      // In local development, use the chats directory
      const isLambda = process.env.AWS_LAMBDA_FUNCTION_NAME !== undefined;
      const chatsDir = isLambda
        ? path.join('/tmp', 'chats')
        : path.join(process.cwd(), 'chats');

      if (!fs.existsSync(chatsDir)) {
        fs.mkdirSync(chatsDir, { recursive: true });
      }

      const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
      const random = Math.floor(Math.random() * 1e6);
      const filename = `chat_${timestamp}_${random}.json`;
      const filePath = path.join(chatsDir, filename);
      const data = {
        createdAt: new Date().toISOString(),
        format,
        messages,
        environment: isLambda ? 'lambda' : 'local'
      };

      await fsPromises.writeFile(filePath, JSON.stringify(data, null, 2), 'utf-8');
      console.log(`Chat session saved to: ${filePath}`);
    } catch (error) {
      // In Lambda, file storage is not critical, so we'll log the error but not fail
      console.warn('Failed to save chat session:', error);
      if (!process.env.AWS_LAMBDA_FUNCTION_NAME) {
        // In local development, we want to know about storage issues
        throw error;
      }
    }
  }
}
