// In-memory conversation storage (for demo - in production use database)
const conversations: Map<string, any[]> = new Map();

export class ConversationService {
  static getConversation(conversationId: string): any[] {
    return conversations.get(conversationId) || [];
  }

  static setConversation(conversationId: string, messages: any[]): void {
    conversations.set(conversationId, messages);
  }

  static addMessage(conversationId: string, message: any): any[] {
    let conversationHistory = this.getConversation(conversationId);
    conversationHistory.push(message);
    this.setConversation(conversationId, conversationHistory);
    return conversationHistory;
  }

  static generateConversationId(): string {
    return `conv_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }
}
