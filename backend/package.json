{"name": "ai-chatbot-backend", "version": "1.0.0", "description": "Backend API for AI Chatbot with OpenAI integration", "main": "dist/server.js", "type": "module", "devDependencies": {"@types/cors": "^2.8.19", "@types/dotenv": "^8.2.0", "@types/express": "^4.17.0", "@types/fs-extra": "^11.0.0", "@types/multer": "^1.4.7", "@types/node": "^20.19.7", "ts-node-dev": "^2.0.0", "tsx": "^4.20.3", "typescript": "^5.9.2"}, "scripts": {"build": "tsc --project tsconfig.json", "start": "npm run build && node dist/server.js", "dev": "tsx watch server.ts"}, "dependencies": {"@vendia/serverless-express": "^4.12.6", "cors": "^2.8.5", "dotenv": "^16.3.1", "express": "^4.18.2", "multer": "^1.4.5-lts.1", "openai": "^4.20.0"}}