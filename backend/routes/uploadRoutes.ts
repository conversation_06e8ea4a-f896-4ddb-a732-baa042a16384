import { Router, Request, Response } from 'express';
import { upload } from '../middleware/uploadMiddleware.js';
import { ChatService } from '../services/chatService.js';
import { FileService } from '../services/fileService.js';

const router = Router();

// Image upload and analysis endpoint
router.post('/upload', upload.single('image'), async (req: Request, res: Response) => {
  try {
    if (!req.file) {
      return res.status(400).json({ error: 'No image file uploaded' });
    }

    const { message, format } = req.body;
    const imagePath = req.file.path;

    // Convert image to base64
    const base64Image = FileService.convertImageToBase64(imagePath);
    const mimeType = req.file.mimetype;

    const result = await ChatService.processImageAnalysis(
      message || "Please analyze this design and help me estimate the project.",
      format,
      base64Image,
      mimeType
    );

    // Clean up uploaded file
    FileService.cleanupFile(imagePath);

    res.json(result);

  } catch (error: unknown) {
    console.error('Upload API Error:', error);
    
    // Clean up file if it exists
    if (req.file) {
      FileService.cleanupFile(req.file.path);
    }
    
    const errorMessage = error instanceof Error ? error.message : 'Unknown error';
    res.status(500).json({ 
      error: 'Failed to process image upload',
      details: errorMessage 
    });
  }
});

export default router;
