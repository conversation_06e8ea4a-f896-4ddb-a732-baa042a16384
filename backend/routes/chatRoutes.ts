import { Router, Request, Response } from 'express';
import { ChatService } from '../services/chatService.js';

const router = Router();

// Text-based chat endpoint
router.post('/chat', async (req: Request, res: Response) => {
  try {
    const { messages, format, conversationId, assumptionMode } = req.body;
    const result = await ChatService.processChat(messages, format, conversationId, assumptionMode);
    res.json(result);
  } catch (error: unknown) {
    console.error('Chat API Error:', error);
    const errorMessage = error instanceof Error ? error.message : 'Unknown error';
    res.status(500).json({ 
      error: 'Failed to process chat request',
      details: errorMessage 
    });
  }
});

// Follow-up chat endpoint
router.post('/followup', async (req: Request, res: Response) => {
  try {
    const { messages, conversationId, assumptionMode } = req.body;
    const result = await ChatService.processFollowup(messages, conversationId, assumptionMode);
    res.json(result);
  } catch (error: unknown) {
    console.error('Follow-up API Error:', error);
    const errorMessage = error instanceof Error ? error.message : 'Unknown error';
    res.status(500).json({ 
      error: 'Failed to process follow-up request',
      details: errorMessage 
    });
  }
});

export default router;
