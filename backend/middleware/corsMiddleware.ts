import cors from 'cors';

// Determine allowed origins based on environment
const getAllowedOrigins = () => {
  const isProduction = process.env.NODE_ENV === 'production';
  const isLambda = process.env.AWS_LAMBDA_FUNCTION_NAME !== undefined;

  if (isProduction || isLambda) {
    // Production origins
    return [
      'https://quote-gen.bloodandtreasure.com',
      'https://d390a06772cam7.cloudfront.net', // CloudFront domain
      /https:\/\/.*\.cloudfront\.net$/, // Any CloudFront domain
      'https://bloodandtreasure.com',
      /https:\/\/.*\.bloodandtreasure\.com$/ // Any subdomain
    ];
  } else {
    // Development origins
    return [
      'http://localhost:5173',
      'http://localhost:5174',
      'http://localhost:5175',
      'http://localhost:3000'
    ];
  }
};

export const corsMiddleware = cors({
  origin: getAllowedOrigins(),
  credentials: true,
  methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
  allowedHeaders: ['Content-Type', 'Authorization', 'X-Requested-With', 'Accept', 'Origin']
});
