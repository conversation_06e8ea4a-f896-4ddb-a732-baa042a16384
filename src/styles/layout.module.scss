@use 'variables' as *;

/* Layout and container styles */

.app {
  width: 100%;
  max-width: 800px;
  margin: 0 auto;

  @include responsive('mobile') {
    padding: var(--spacing-xs);
  }
}

.chatbotContainer {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-xl);
  width: 100%;
}

.card {
  position: relative;
  background: var(--bg-tertiary);
  @include glass-border();
  border-radius: var(--radius-xl);
  padding: var(--spacing-4xl);
  box-shadow:
    0 8px 32px rgba(0, 0, 0, 0.3),
    var(--shadow-sm),
    var(--shadow-glass);
  @include glass-effect(0, 20px);
  @include gradient-border(var(--gradient-rainbow), var(--radius-xl));

  // Remove default animation - will be added conditionally
  &::before {
    animation: none;
  }

  &::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: radial-gradient(
      circle at 50% 50%,
      rgba(255, 255, 255, 0.05) 0%,
      transparent 70%
    );
    border-radius: var(--radius-xl);
    pointer-events: none;
    z-index: 1;
  }

  @include responsive('tablet') {
    padding: var(--spacing-2xl);
    border-radius: var(--radius-lg);

    &::before,
    &::after {
      border-radius: var(--radius-lg);
    }
  }

  @include responsive('mobile') {
    padding: var(--spacing-xl);
    border-radius: var(--radius-md);

    &::before,
    &::after {
      border-radius: var(--radius-md);
    }
  }
}

.mainCard {
  composes: card;
  background: var(--bg-secondary);
  transition: var(--transition-normal);

  // Default yellow border (no animation)
  &::before {
    background: var(--color-primary);
    animation: none;
  }

  // Only animate when loading (user is asking a question)
  &.loading::before {
    background: var(--gradient-rainbow);
    background-size: 300% 300%;
    animation: gradientShift var(--animation-normal) ease infinite;
  }

  &:hover {
    @include hover-lift();
    box-shadow:
      var(--shadow-xl),
      0 4px 12px rgba(0, 0, 0, 0.3),
      var(--shadow-glass-hover);

    // Only speed up animation on hover if we're in loading state
    &.loading::before {
      animation-duration: var(--animation-fast);
    }
  }
}

.formatCard {
  composes: card;
  padding: var(--spacing-xl);
  background: var(--bg-card);

  &::before {
    background: linear-gradient(
      90deg,
      #0ea5e9,
      #3a86ff,
      #38bdf8,
      #06ffa5,
      #ffbe0b,
      #fb5607,
      #0ea5e9
    );
    background-size: 400% 400%;
    animation: gradientFlow 12s ease infinite;
  }
}

.responseCard {
  composes: card;
  margin-top: var(--spacing-xl);
  display: none;
  background: rgba(25, 25, 40, 0.9);

  &.visible {
    display: block;
    animation: slideUpGlass 0.5s cubic-bezier(0.4, 0, 0.2, 1);
  }
}

.title {
  font-size: var(--font-size-5xl);
  font-weight: var(--font-weight-semibold);
  background: var(--gradient-text);
  background-clip: text;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  margin-bottom: var(--spacing-lg);
  text-align: left;
  font-family: var(--font-family);
  position: relative;
  z-index: 2;

  @include responsive('tablet') {
    font-size: var(--font-size-4xl);
  }

  @include responsive('mobile') {
    font-size: var(--font-size-3xl);
  }
}

.description {
  font-size: var(--font-size-lg);
  color: var(--text-tertiary);
  margin-bottom: var(--spacing-2xl);
  text-align: left;
  line-height: var(--line-height-tight);
  font-family: var(--font-family);
  position: relative;
  z-index: 2;
}

.inputSection {
  display: flex;
  flex-direction: column;
  gap: 0;
  position: relative;
  z-index: 2;
}

.actionButtons {
  display: flex;
  align-items: center;
  gap: var(--spacing-md);
  flex-wrap: wrap;
  // position: relative;
  z-index: 2;

  @include responsive('tablet') {
    flex-direction: column;
    align-items: stretch;
  }
}

.formatQuestion {
  display: flex;
  align-items: center;
  justify-content: space-between;
  flex-wrap: wrap;
  gap: var(--spacing-lg);
  position: relative;
  z-index: 2;

  span {
    font-size: var(--font-size-lg);
    color: var(--text-secondary);
    font-weight: var(--font-weight-medium);
  }

  @include responsive('tablet') {
    flex-direction: column;
    align-items: stretch;
    text-align: center;
  }
}

.formatOptions {
  display: flex;
  gap: var(--spacing-md);
  flex-wrap: wrap;
  align-items: center;

  @include responsive('tablet') {
    justify-content: center;
  }

  @include responsive('mobile') {
    flex-direction: column;
  }
}

.responseHeader {
  font-size: var(--font-size-2xl);
  font-weight: var(--font-weight-semibold);
  color: rgba(255, 255, 255, 0.95);
  margin-bottom: var(--spacing-lg);
}

.responseContent {
  font-size: var(--font-size-md);
  line-height: var(--line-height-normal);
  color: rgba(255, 255, 255, 0.85);
}

.imageAnalyzedBadge {
  @include glass-effect(0, 10px);
  background: rgba(3, 105, 161, 0.2);
  border: 1px solid rgba(3, 105, 161, 0.3);
  color: #60a5fa;
  padding: var(--spacing-md);
  border-radius: var(--radius-md);
  font-size: var(--font-size-base);
  font-weight: var(--font-weight-medium);
  margin-bottom: var(--spacing-lg);
  display: inline-block;
}

.errorMessage {
  @include glass-effect(0, 10px);
  background: rgba(220, 38, 38, 0.1);
  border: 1px solid rgba(220, 38, 38, 0.3);
  border-radius: var(--radius-md);
  padding: var(--spacing-lg);
  color: #fca5a5;

  p {
    margin-bottom: var(--spacing-sm);

    &:last-child {
      margin-bottom: 0;
    }
  }
}

/* Character counter */
.characterCounter {
  position: absolute;
  bottom: var(--spacing-sm);
  right: var(--spacing-md);
  font-size: var(--font-size-sm);
  color: var(--text-muted);
  pointer-events: none;
  z-index: 3;

  &.warning {
    color: var(--border-warning);
  }

  &.error {
    color: var(--border-error);
  }
}

/* Assumption Toggle Styles */
.assumptionToggle {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-md);
  padding: var(--spacing-lg);
  @include glass-effect(0.03);
  @include glass-border(0.15);
  border-radius: var(--radius-md);
  background: rgba(139, 92, 246, 0.05);
  position: relative;
  z-index: 2;

  @include responsive('mobile') {
    padding: var(--spacing-md);
  }
}

.toggleLabel {
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-medium);
  color: var(--text-secondary);
  margin-bottom: var(--spacing-xs);
}

.toggleButtons {
  display: flex;
  gap: var(--spacing-sm);
  
  @include responsive('mobile') {
    flex-direction: column;
  }
}

.toggleDescription {
  font-size: var(--font-size-xs);
  color: var(--text-muted);
  line-height: var(--line-height-tight);
  margin-top: var(--spacing-xs);
  font-style: italic;
}

/* Assumption Options Styles */
.assumptionOptionsContainer {
  margin: var(--spacing-lg) 0;
  padding: var(--spacing-lg);
  @include glass-effect(0.03);
  @include glass-border(0.15);
  border-radius: var(--radius-md);
  background: rgba(139, 92, 246, 0.05);
}

.assumptionOptionsHeader {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-medium);
  color: var(--text-secondary);
  margin-bottom: var(--spacing-md);
  
  svg {
    color: rgba(139, 92, 246, 0.8);
  }
}

.assumptionOptionCard {
  margin-bottom: var(--spacing-md);
  padding: var(--spacing-md);
  @include glass-effect(0.02);
  @include glass-border(0.1);
  border-radius: var(--radius-sm);
  background: rgba(255, 255, 255, 0.02);
  
  &:last-child {
    margin-bottom: 0;
  }
}

.questionText {
  display: flex;
  align-items: flex-start;
  gap: var(--spacing-sm);
  font-size: var(--font-size-sm);
  color: var(--text-tertiary);
  margin-bottom: var(--spacing-sm);
  line-height: var(--line-height-tight);
  
  svg {
    color: rgba(59, 130, 246, 0.7);
    margin-top: 2px;
    flex-shrink: 0;
  }
}

.assumptionOptionsFooter {
  font-size: var(--font-size-xs);
  color: var(--text-muted);
  text-align: center;
  margin-top: var(--spacing-md);
  padding-top: var(--spacing-md);
  border-top: 1px solid rgba(255, 255, 255, 0.1);
  font-style: italic;
}

/* Question and Assumption Highlighting */
:global(.question-highlight) {
  display: block;
  padding: var(--spacing-md);
  margin: var(--spacing-sm) 0;
  background: rgba(59, 130, 246, 0.1);
  border-left: 4px solid rgba(59, 130, 246, 0.5);
  border-radius: var(--radius-sm);
  color: rgba(147, 197, 253, 0.95);
  
  .question-icon {
    margin-right: var(--spacing-xs);
    font-size: var(--font-size-sm);
  }
}

:global(.assumption-highlight) {
  display: block;
  padding: var(--spacing-md);
  margin: var(--spacing-sm) 0;
  background: rgba(139, 92, 246, 0.1);
  border-left: 4px solid rgba(139, 92, 246, 0.5);
  border-radius: var(--radius-sm);
  color: rgba(196, 181, 253, 0.95);
  
  .assumption-icon {
    margin-right: var(--spacing-xs);
    font-size: var(--font-size-sm);
  }
}

/* Animations */
@keyframes gradientShift {
  0%, 100% { background-position: 0% 50%; }
  25% { background-position: 100% 50%; }
  50% { background-position: 200% 50%; }
  75% { background-position: 300% 50%; }
}

@keyframes gradientFlow {
  0%, 100% { background-position: 0% 50%; }
  25% { background-position: 100% 50%; }
  50% { background-position: 200% 50%; }
  75% { background-position: 300% 50%; }
}

@keyframes slideUpGlass {
  from {
    opacity: 0;
    transform: translateY(30px) scale(0.95);
    backdrop-filter: blur(0px);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
    backdrop-filter: blur(20px);
  }
}

/* High contrast mode */
@media (prefers-contrast: high) {
  .card {
    border-color: rgba(255, 255, 255, 0.5);
    background: rgba(0, 0, 0, 0.9);
  }
  
  .title {
    -webkit-text-fill-color: white;
    background: white;
  }
  
  .description {
    color: #fff;
  }
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
  .card::before,
  .mainCard,
  .responseCard.visible {
    animation: none;
    transition: none;
  }
  
  .mainCard:hover {
    transform: none;
  }
}

/* Dark mode enhancement */
@media (prefers-color-scheme: dark) {
  .card {
    background: rgba(5, 5, 15, 0.9);
    border-color: rgba(255, 255, 255, 0.15);
  }
  
  .mainCard {
    background: rgba(0, 0, 10, 0.95);
  }
  
  .formatCard {
    background: rgba(10, 10, 25, 0.9);
  }
  
  .responseCard {
    background: rgba(15, 15, 30, 0.95);
  }
}
