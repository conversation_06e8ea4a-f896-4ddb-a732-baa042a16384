@use 'sass:map';

// CSS Variables and SCSS Variables
:root {
  // Color Palette
  --color-primary: #ffce27;
  --color-primary-dark: #e6b800;
  --color-primary-darker: #a17102;
  --color-secondary: #0ea5e9;
  --color-secondary-light: #38bdf8;
  --color-accent-blue: #3a86ff;
  --color-accent-blue-dark: #4361ee;
  --color-accent-pink: #ff006e;
  --color-accent-green: #06ffa5;
  --color-accent-orange: #fb5607;
  --color-accent-yellow: #ffbe0b;
  
  // Background Colors
  --bg-primary: #0a0a0f;
  --bg-secondary: rgba(10, 10, 20, 0.9);
  --bg-tertiary: rgba(15, 15, 25, 0.85);
  --bg-card: rgba(20, 20, 35, 0.85);
  --bg-modal: rgba(10, 10, 25, 0.9);
  --bg-glass: rgba(255, 255, 255, 0.05);
  --bg-glass-hover: rgba(255, 255, 255, 0.08);
  --bg-glass-light: rgba(255, 255, 255, 0.03);
  
  // Text Colors
  --text-primary: #ffffff;
  --text-secondary: rgba(255, 255, 255, 0.9);
  --text-tertiary: rgba(255, 255, 255, 0.8);
  --text-muted: rgba(255, 255, 255, 0.6);
  --text-placeholder: rgba(255, 255, 255, 0.233);
  --text-disabled: rgba(255, 255, 255, 0.4);
  
  // Border Colors
  --border-primary: rgba(255, 255, 255, 0.1);
  --border-secondary: rgba(255, 255, 255, 0.2);
  --border-tertiary: rgba(255, 255, 255, 0.15);
  --border-focus: var(--color-secondary);
  --border-error: #f87171;
  --border-warning: #fbbf24;
  
  // Status Colors
  --color-success: #10b981;
  --color-warning: #f59e0b;
  --color-error: #ef4444;
  --color-info: #3b82f6;
  
  // Shadows
  --shadow-sm: 0 2px 8px rgba(0, 0, 0, 0.1);
  --shadow-md: 0 4px 12px rgba(0, 0, 0, 0.1);
  --shadow-lg: 0 8px 20px rgba(0, 0, 0, 0.2);
  --shadow-xl: 0 12px 40px rgba(0, 0, 0, 0.4);
  --shadow-2xl: 0 25px 50px rgba(0, 0, 0, 0.5);
  --shadow-glass: inset 0 1px 0 rgba(255, 255, 255, 0.1);
  --shadow-glass-hover: inset 0 1px 0 rgba(255, 255, 255, 0.15);
  
  // Gradients
  --gradient-primary: linear-gradient(135deg, var(--color-primary), var(--color-accent-orange));
  --gradient-secondary: linear-gradient(135deg, var(--color-secondary), var(--color-accent-blue));
  --gradient-text: linear-gradient(135deg, #ffffff, #e0e0e0, #ffffff);
  --gradient-rainbow: linear-gradient(45deg, var(--color-accent-blue), var(--color-secondary), var(--color-accent-blue-dark), var(--color-accent-green), var(--color-accent-yellow), var(--color-accent-orange), var(--color-accent-blue));
  --gradient-background: radial-gradient(circle at 20% 80%, rgba(14, 165, 233, 0.3) 0%, transparent 50%), radial-gradient(circle at 80% 20%, rgba(56, 189, 248, 0.3) 0%, transparent 50%), radial-gradient(circle at 40% 40%, rgba(120, 219, 255, 0.2) 0%, transparent 50%), linear-gradient(135deg, rgba(0, 0, 0, 0.9) 0%, rgba(0, 24, 62, 0.9) 100%);
  
  // Spacing
  --spacing-xs: 4px;
  --spacing-sm: 8px;
  --spacing-md: 12px;
  --spacing-lg: 16px;
  --spacing-xl: 20px;
  --spacing-2xl: 24px;
  --spacing-3xl: 28px;
  --spacing-4xl: 32px;
  
  // Border Radius
  --radius-sm: 8px;
  --radius-md: 12px;
  --radius-lg: 16px;
  --radius-xl: 20px;
  --radius-2xl: 24px;
  --radius-full: 50%;
  
  // Typography
  --font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Inter', 'Oxygen', 'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue', sans-serif;
  --font-size-xs: 10px;
  --font-size-sm: 12px;
  --font-size-base: 14px;
  --font-size-md: 15px;
  --font-size-lg: 16px;
  --font-size-xl: 18px;
  --font-size-2xl: 20px;
  --font-size-3xl: 22px;
  --font-size-4xl: 24px;
  --font-size-5xl: 28px;
  --font-weight-normal: 400;
  --font-weight-medium: 500;
  --font-weight-semibold: 600;
  --line-height-tight: 1.5;
  --line-height-normal: 1.6;
  
  // Transitions
  --transition-fast: 0.2s ease;
  --transition-normal: 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  --transition-slow: 0.6s ease;
  
  // Backdrop Filters
  --backdrop-blur-sm: blur(10px);
  --backdrop-blur-md: blur(12px);
  --backdrop-blur-lg: blur(20px);
  --backdrop-blur-xl: blur(25px);
  --backdrop-saturate: saturate(180%);
  --backdrop-saturate-high: saturate(200%);
  
  // Z-Index
  --z-dropdown: 1000;
  --z-modal: 2000;
  --z-splash: 3000;
  
  // Animation Durations
  --animation-fast: 4s;
  --animation-normal: 8s;
  --animation-slow: 12s;
  --animation-very-slow: 16s;
}

// SCSS Variables for calculations and mixins
$breakpoints: (
  'mobile': 480px,
  'tablet': 768px,
  'desktop': 1024px,
  'wide': 1200px
);

// Mixins
@mixin glass-effect($opacity: 0.05, $blur: 10px) {
  background: rgba(255, 255, 255, $opacity);
  backdrop-filter: blur($blur) var(--backdrop-saturate);
  -webkit-backdrop-filter: blur($blur) var(--backdrop-saturate);
}

@mixin glass-border($opacity: 0.1) {
  border: 1px solid rgba(255, 255, 255, $opacity);
}

@mixin hover-lift($distance: -2px) {
  transition: var(--transition-normal);
  
  &:hover:not(:disabled) {
    transform: translateY($distance);
  }
}

@mixin gradient-dashed-border($gradient: var(--gradient-rainbow), $radius: var(--radius-md)) {
  position: relative;
  
  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    border-radius: $radius;
    padding: 2px;
    background: $gradient;
    background-size: 300% 300%;
    mask: linear-gradient(#fff 0 0) content-box, linear-gradient(#fff 0 0);
    mask-composite: xor;
    -webkit-mask: linear-gradient(#fff 0 0) content-box, linear-gradient(#fff 0 0);
    -webkit-mask-composite: xor;
    z-index: -1;
  }
}

@mixin gradient-border($gradient: var(--gradient-rainbow), $radius: var(--radius-md)) {
  position: relative;
  
  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    border-radius: $radius;
    padding: 2px;
    background: $gradient;
    background-size: 300% 300%;
    mask: linear-gradient(#fff 0 0) content-box, linear-gradient(#fff 0 0);
    mask-composite: xor;
    -webkit-mask: linear-gradient(#fff 0 0) content-box, linear-gradient(#fff 0 0);
    -webkit-mask-composite: xor;
    z-index: -1;
  }
}

@mixin shimmer-effect() {
  &::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(45deg, transparent, rgba(255, 255, 255, 0.1), transparent);
    transform: translateX(-100%);
    transition: transform var(--transition-slow);
    z-index: 1;
    pointer-events: none;
  }
  
  &:hover::after {
    transform: translateX(100%);
  }
}

@mixin responsive($breakpoint) {
  @if map.has-key($breakpoints, $breakpoint) {
    @media (max-width: map.get($breakpoints, $breakpoint)) {
      @content;
    }
  }
}

@mixin focus-ring($color: var(--color-secondary)) {
  &:focus-visible {
    outline: 2px solid $color;
    outline-offset: 2px;
  }
}

@mixin disabled-state() {
  &:disabled {
    opacity: 0.4;
    cursor: not-allowed;
    transform: none !important;
    
    &::before,
    &::after {
      display: none;
    }
  }
}
