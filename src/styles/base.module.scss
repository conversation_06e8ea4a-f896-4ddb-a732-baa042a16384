@use 'variables' as *;

/* Base styles and CSS reset with Enhanced Glass Effects */

.body {
  margin: 0;
  // background-color: var(--bg-primary);
  color: var(--text-primary);
  line-height: var(--line-height-normal);
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: var(--spacing-xl);
  position: relative;
  font-family: var(--font-family);
}

/* Enhanced background overlay with particle effects */
.backgroundOverlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: var(--gradient-background);
  z-index: -1;
  pointer-events: none;
  animation: backgroundPulse 8s ease-in-out infinite alternate;

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-image: 
      radial-gradient(2px 2px at 20px 30px, rgba(255, 255, 255, 0.1), transparent),
      radial-gradient(2px 2px at 40px 70px, rgba(255, 255, 255, 0.05), transparent),
      radial-gradient(1px 1px at 90px 40px, rgba(255, 255, 255, 0.08), transparent),
      radial-gradient(1px 1px at 130px 80px, rgba(255, 255, 255, 0.03), transparent);
    background-repeat: repeat;
    background-size: 150px 100px;
    animation: particleFloat 20s linear infinite;
    opacity: 0.6;
  }
}

/* Desaturate and darken the background image */
body::before {
  content: '';
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: -2;
  pointer-events: none;
  background: inherit;
  filter: grayscale(0.8) brightness(0.4) contrast(1.2) saturate(1.5);
}

/* Enhanced photo attribution with glass effect */
.photoAttribution {
  position: fixed;
  bottom: var(--spacing-xl);
  left: var(--spacing-xl);
  @include glass-effect(0.6, 12px);
  border: 1.5px solid var(--color-primary-darker);
  color: var(--text-secondary);
  padding: var(--spacing-xs) var(--spacing-sm);
  border-radius: 40px;
  font-size: var(--font-size-sm);
  z-index: var(--z-dropdown);
  display: flex;
  align-items: center;
  gap: 6px;
  box-shadow: 
    var(--shadow-md),
    var(--shadow-glass);
  transition: var(--transition-normal);
  background-color: #00000055;

  &:hover {
    @include glass-effect(0.7, 12px);
    cursor: pointer;
    background-color: rgba(142, 0, 0, 0.1);
    border-color: rgba(255, 206, 39, 0.5);
    transform: translateY(-2px);
    box-shadow: 
      var(--shadow-lg),
      var(--shadow-glass-hover);
  }

  a {
    color: var(--color-primary);
    text-decoration: none;
    font-weight: var(--font-weight-medium);
    transition: color var(--transition-fast);

    &:hover {
      color: #ffd700;
      text-decoration: underline;
    }
  }

  @include responsive('tablet') {
    bottom: var(--spacing-lg);
    left: var(--spacing-lg);
    font-size: 11px;
    padding: var(--spacing-sm) var(--spacing-md);
    border-radius: var(--radius-md);
  }

  @include responsive('mobile') {
    bottom: var(--spacing-md);
    left: var(--spacing-md);
    font-size: var(--font-size-xs);
    padding: 6px var(--spacing-xs);
    border-radius: var(--radius-sm);
  }
}

/* Enhanced background refresh button */
.backgroundRefresh {
  position: fixed;
  top: var(--spacing-2xl);
  right: var(--spacing-2xl);
  @include glass-effect(0.6, 12px);
  color: var(--text-secondary);
  @include glass-border();
  width: 48px;
  height: 48px;
  cursor: pointer;
  font-size: var(--font-size-xl);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: var(--z-dropdown);
  transition: var(--transition-normal);
  border-radius: var(--radius-lg);
  box-shadow: 
    var(--shadow-md),
    var(--shadow-glass);
  position: relative;
  overflow: hidden;
  @include shimmer-effect();

  &:hover {
    @include glass-effect(0.7, 12px);
    border-color: var(--border-secondary);
    transform: translateY(-2px);
    box-shadow: 
      var(--shadow-lg),
      var(--shadow-glass-hover);
  }

  &:active {
    transform: translateY(0);
  }

  @include disabled-state();

  @include responsive('tablet') {
    top: var(--spacing-xl);
    right: var(--spacing-xl);
    width: 44px;
    height: 44px;
    border-radius: var(--radius-md);
  }

  @include responsive('mobile') {
    top: var(--spacing-lg);
    right: var(--spacing-lg);
    width: 40px;
    height: 40px;
    border-radius: var(--radius-sm);
  }
}

/* Enhanced loading spinner */
.spinner {
  animation: spinGlow 5s linear infinite;
}

/* Enhanced splash screen with glass effects */
.splashOverlayV2 {
  position: fixed;
  z-index: var(--z-splash);
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  background: 
    radial-gradient(circle at 30% 20%, rgba(236, 56, 56, 0.3) 0%, transparent 50%),
    radial-gradient(circle at 70% 80%, rgba(34, 34, 34, 0.3) 0%, transparent 50%),
    linear-gradient(135deg, rgba(23, 6, 0, 0.95) 0%, rgba(0, 0, 0, 0.95) 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 1;
  pointer-events: all;
  transition: opacity var(--transition-slow);
  backdrop-filter: var(--backdrop-blur-lg);
  -webkit-backdrop-filter: var(--backdrop-blur-lg);

  &--fade {
    opacity: 0;
    pointer-events: none;
    transition: opacity var(--transition-slow);
  }
}

.splashLogoWrapper {
  position: relative;
  width: 220px;
  height: 220px;
  display: flex;
  align-items: center;
  justify-content: center;
  @include gradient-dashed-border(var(--gradient-rainbow), 32px);

  &::before {
    animation: splashBorderPulse 3s ease-in-out infinite alternate;
  }

  @include responsive('tablet') {
    width: 180px;
    height: 180px;

    &::before {
      width: 180px;
      height: 180px;
      border-radius: var(--radius-2xl);
    }
  }

  @include responsive('mobile') {
    width: 160px;
    height: 160px;

    &::before {
      width: 160px;
      height: 160px;
      border-radius: var(--radius-xl);
    }
  }
}

.splashLogoV2 {
  width: 200px;
  height: 200px;
  object-fit: contain;
  border-radius: var(--radius-2xl);
  box-shadow: 
    0 8px 32px rgba(0, 0, 0, 0.4),
    0 4px 16px rgba(131, 56, 236, 0.3),
    var(--shadow-glass);
  animation: logoGlow 3s ease-in-out infinite alternate;
  border: 2px solid rgba(255, 206, 39, 0.3);
  z-index: 1;
  position: relative;
  @include glass-effect(0, 10px);

  @include responsive('tablet') {
    width: 160px;
    height: 160px;
    border-radius: var(--radius-xl);
  }

  @include responsive('mobile') {
    width: 140px;
    height: 140px;
    border-radius: var(--radius-lg);
  }
}

/* Animations */
@keyframes backgroundPulse {
  0% { opacity: 0.8; }
  100% { opacity: 1; }
}

@keyframes particleFloat {
  0% { transform: translateY(0px); }
  100% { transform: translateY(-100px); }
}

@keyframes spinGlow {
  from { 
    transform: rotate(0deg);
    filter: drop-shadow(0 0 4px rgba(255, 255, 255, 0.5));
  }
  to { 
    transform: rotate(360deg);
    filter: drop-shadow(0 0 8px rgba(255, 255, 255, 0.8));
  }
}

@keyframes splashBorderPulse {
  0% {
    background-position: 0% 50%;
    transform: scale(1);
    filter: drop-shadow(0 0 10px rgba(131, 56, 236, 0.5));
  }
  25% {
    background-position: 66% 50%;
    transform: scale(1.025);
    filter: drop-shadow(0 0 18px rgba(58, 134, 255, 0.6));
  }
  50% {
    background-position: 100% 50%;
    transform: scale(1.05);
    filter: drop-shadow(0 0 24px rgba(255, 190, 11, 0.7));
  }
  75% {
    background-position: 133% 50%;
    transform: scale(1.025);
    filter: drop-shadow(0 0 18px rgba(58, 134, 255, 0.6));
  }
  100% {
    background-position: 200% 50%;
    transform: scale(1);
    filter: drop-shadow(0 0 10px rgba(131, 56, 236, 0.5));
  }
}

@keyframes logoGlow {
  0% {
    box-shadow: 
      0 8px 32px rgba(0, 0, 0, 0.4),
      0 4px 16px rgba(131, 56, 236, 0.3),
      var(--shadow-glass);
    transform: scale(1);
  }
  50% {
    box-shadow: 
      0 12px 40px rgba(0, 0, 0, 0.5),
      0 8px 24px rgba(58, 134, 255, 0.4),
      var(--shadow-glass-hover);
    transform: scale(1.02);
  }
  100% {
    box-shadow: 
      0 10px 36px rgba(0, 0, 0, 0.45),
      0 6px 20px rgba(255, 190, 11, 0.35),
      inset 0 1px 0 rgba(255, 255, 255, 0.12);
    transform: scale(1);
  }
}

/* High contrast mode support */
@media (prefers-contrast: high) {
  .body {
    background-color: #000;
    color: #fff;
  }
  
  .backgroundOverlay {
    background: rgba(255, 255, 255, 0.1);
  }
  
  .photoAttribution {
    background: rgba(0, 0, 0, 0.9);
    border-color: #fff;
  }
  
  .backgroundRefresh {
    background: rgba(0, 0, 0, 0.9);
    border-color: #fff;
  }
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
  .spinner,
  .backgroundRefresh,
  .backgroundOverlay,
  .backgroundOverlay::before,
  .splashLogoWrapper::before,
  .splashLogoV2,
  .photoAttribution {
    animation: none;
    transition: none;
  }
  
  .backgroundRefresh::before {
    display: none;
  }
  
  .photoAttribution:hover,
  .backgroundRefresh:hover {
    transform: none;
  }
}

/* Print styles */
@media print {
  .backgroundRefresh,
  .photoAttribution,
  .backgroundOverlay,
  .splashOverlayV2 {
    display: none;
  }
  
  .body {
    background: white;
    color: black;
  }
}
