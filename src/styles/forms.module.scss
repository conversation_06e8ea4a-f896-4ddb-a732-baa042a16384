@use 'variables' as *;

/* Form and input styles with Dark Glass Effects */

.projectInput {
  position: relative;
  margin-bottom: var(--spacing-xl);
  padding: var(--spacing-xl);
  @include glass-effect(0.05);
  border: 2px solid var(--border-primary);
  border-radius: var(--radius-lg);
  font-size: var(--font-size-lg);
  line-height: var(--line-height-normal);
  color: var(--text-secondary);
  min-height: 100px;
  font-family: var(--font-family);
  resize: none;
  transition: var(--transition-normal);
  width: 100%;
  box-sizing: border-box;
  @include glass-effect(0.05, 10px);
  box-shadow: 
    var(--shadow-md),
    var(--shadow-glass);
  @include gradient-border(linear-gradient(45deg, transparent, rgba(255, 255, 255, 0.1), transparent));

  &::before {
    opacity: 0;
    transition: opacity var(--transition-normal);
    pointer-events: none;
  }

  &:focus {
    outline: none;
    border-color: var(--border-focus);
    @include glass-effect(0.08);
    box-shadow: 
      0 0 0 4px rgba(14, 165, 233, 0.2),
      var(--shadow-lg),
      var(--shadow-glass-hover);
    transform: translateY(-1px);

    &::before {
      opacity: 1;
      background: var(--gradient-rainbow);
      background-size: 300% 300%;
      animation: inputGradientShift var(--animation-very-slow) ease infinite;
    }
  }

  &::placeholder {
    color: var(--text-placeholder);
    line-height: var(--line-height-normal);
    font-size: var(--font-size-lg);
    font-weight: var(--font-weight-normal);
  }

  &.warning {
    border-color: var(--border-warning);
    box-shadow: 
      0 0 0 3px rgba(251, 191, 36, 0.2),
      var(--shadow-md);
  }

  &.error {
    border-color: var(--border-error);
    box-shadow: 
      0 0 0 3px rgba(248, 113, 113, 0.2),
      var(--shadow-md);
  }

  @include responsive('tablet') {
    border-radius: var(--radius-md);

    &::before {
      border-radius: var(--radius-md);
    }
  }

  @include focus-ring();
}

/* File input styles */
.fileInput {
  display: none;
}

/* Image preview with glass effect */
.imagePreview {
  position: relative;
  margin-top: var(--spacing-lg);
  @include glass-effect(0.05);
  border: 2px solid var(--border-primary);
  border-radius: var(--radius-lg);
  overflow: hidden;
  max-width: 300px;
  display: none;
  @include glass-effect(0.05, 10px);
  box-shadow: var(--shadow-lg);

  &.visible {
    display: block;
    animation: fadeInGlass 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  }

  img {
    width: 100%;
    height: auto;
    display: block;
  }

  &:hover .imagePreviewOverlay {
    opacity: 1;
  }

  @include responsive('tablet') {
    max-width: 100%;
  }
}

.imagePreviewOverlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.6);
  backdrop-filter: blur(4px);
  -webkit-backdrop-filter: blur(4px);
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0;
  transition: var(--transition-normal);
}

/* Dropdown styles with glass effect */
.dropdown {
  position: relative;
  display: inline-block;

  @include responsive('mobile') {
    width: 100%;
  }
}

.dropdownMenu {
  position: absolute;
  top: 100%;
  left: 0;
  background: rgba(10, 10, 25, 0.9);
  @include glass-border();
  border-radius: var(--radius-lg);
  @include glass-effect(0, 20px);
  box-shadow: 
    0 10px 30px rgba(0, 0, 0, 0.3),
    var(--shadow-md);
  z-index: var(--z-dropdown);
  opacity: 0;
  visibility: hidden;
  transform: translateY(-10px) scale(0.95);
  transition: var(--transition-normal);
  max-height: 0px;
  max-width: 0px;
  overflow-y: auto;
  overflow: hidden;
  @include gradient-border(linear-gradient(135deg, rgba(255, 255, 255, 0.2), transparent, rgba(255, 255, 255, 0.1)));

  &.show {
    opacity: 1;
    visibility: visible;
    transform: translateY(0) scale(1);
    max-height: 300px;
    min-width: 200px;
    position: absolute;
    overflow-y: scroll;
    background: rgba(10, 10, 25, 0.9);
  }

  @include responsive('tablet') {
    min-width: 180px;
    left: 50%;
    transform: translateX(-50%) translateY(-10px) scale(0.95);

    &.show {
      transform: translateX(-50%) translateY(0) scale(1);
    }
  }

  @include responsive('mobile') {
    width: 100%;
    left: 0;
    transform: translateY(-10px) scale(0.95);
    border-radius: var(--radius-md);

    &::before {
      border-radius: var(--radius-md);
    }

    &.show {
      transform: translateY(0) scale(1);
    }
  }
}

.dropdownItem {
  padding: var(--spacing-md) var(--spacing-lg);
  cursor: pointer;
  font-size: var(--font-size-base);
  color: var(--text-tertiary);
  border-bottom: 1px solid rgba(255, 255, 255, 0.05);
  transition: var(--transition-fast);
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  position: relative;

  &:last-child {
    border-bottom: none;
  }

  &:hover {
    @include glass-effect(0.08);
    color: rgba(255, 255, 255, 0.95);
    transform: translateX(2px);
  }

  &:focus {
    background: rgba(14, 165, 233, 0.2);
    outline: none;
    color: var(--text-primary);
  }

  &.selected {
    background: rgba(14, 165, 233, 0.3);
    color: var(--text-primary);
    font-weight: var(--font-weight-medium);

    &::before {
      width: 1px;
    }
  }

  &:first-child {
    border-top-left-radius: var(--radius-lg);
    border-top-right-radius: var(--radius-lg);

    @include responsive('mobile') {
      border-radius: var(--radius-md) var(--radius-md) 0 0;
    }
  }

  &:last-child {
    border-bottom-left-radius: var(--radius-lg);
    border-bottom-right-radius: var(--radius-lg);

    @include responsive('mobile') {
      border-radius: 0 0 var(--radius-md) var(--radius-md);
    }
  }

  @include focus-ring();
}

/* Form validation */
.fieldError {
  color: var(--border-error);
  font-size: var(--font-size-sm);
  margin-top: var(--spacing-sm);
  display: flex;
  align-items: center;
  gap: 6px;
  padding: var(--spacing-sm) var(--spacing-md);
  background: rgba(248, 113, 113, 0.1);
  border: 1px solid rgba(248, 113, 113, 0.2);
  border-radius: var(--radius-sm);
  @include glass-effect(0, 10px);
}

/* Animations */
@keyframes inputGradientShift {
  0%, 100% { background-position: 0% 50%; }
  25% { background-position: 100% 50%; }
  50% { background-position: 200% 50%; }
  75% { background-position: 300% 50%; }
}

@keyframes fadeInGlass {
  from {
    opacity: 0;
    transform: translateY(20px) scale(0.95);
    backdrop-filter: blur(0px);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
    backdrop-filter: blur(10px);
  }
}

/* High contrast mode */
@media (prefers-contrast: high) {
  .projectInput {
    border-width: 2px;
    border-color: rgba(255, 255, 255, 0.5);
    background: rgba(0, 0, 0, 0.8);

    &:focus {
      border-color: var(--color-secondary);
      box-shadow: 0 0 0 3px rgba(14, 165, 233, 0.5);
    }
  }
  
  .dropdownMenu {
    border-color: var(--border-secondary);
    border-width: 2px;
    background: rgba(0, 0, 0, 0.9);
  }
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
  .projectInput,
  .dropdownMenu,
  .dropdownItem {
    transition: none;
    animation: none;
  }
  
  .projectInput:focus::before {
    animation: none;
  }
  
  .dropdownItem:hover {
    transform: none;
  }
  
  .imagePreview.visible {
    animation: none;
  }
}
