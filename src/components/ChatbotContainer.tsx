import React, { useState, useCallback } from 'react';
import { Pa<PERSON>, ClipboardList } from 'lucide-react';
import { FormatSelector } from './FormatSelector';
import { ImageUpload } from './ImageUpload';
import { FormSubmission } from './FormSubmission';
import { ResponsePopup } from './ResponsePopup';
import { useStateManager } from '@/hooks/useStateManager';
import { useChatHistory } from '@/hooks/useChatHistory';
import { apiService } from '@/services/api';
import { ValidationUtils } from '@/utils/validation';
import { MESSAGES, LABELS, PLACEHOLDERS, VALIDATION_MESSAGES } from '@/constants';
import type { FormData as ChatFormData, PopupState } from '@/types';
import formStyles from '@/styles/forms.module.scss';
import layoutStyles from '@/styles/layout.module.scss';
import buttonStyles from '@/styles/buttons.module.scss';

interface ChatbotContainerProps {
  onNotification: (message: string, type: 'info' | 'success' | 'warning' | 'error') => void;
  className?: string;
}

const ChatbotContainer: React.FC<ChatbotContainerProps> = ({ 
  onNotification, 
  className 
}) => {
  const { state, updateState } = useStateManager();
  const { lastConversation, addConversation, addFollowUp, updateQuoteData, clearHistory } = useChatHistory();
  const [message, setMessage] = useState('');
  const [popupState, setPopupState] = useState<PopupState>({
    isOpen: false,
    response: null,
    imageAnalyzed: false,
    conversationId: undefined,
    followUpHistory: [],
  });
  const [lastApiResponse, setLastApiResponse] = useState<any>(null);
  const [showNewQuoteButton, setShowNewQuoteButton] = useState(false);

  const handleFormatChange = useCallback((format: string) => {
    updateState({ selectedFormat: format });
    console.log('Format changed:', format);
  }, [updateState]);

  const handleImageChange = useCallback((file: File | null) => {
    updateState({ selectedImage: file });
  }, [updateState]);

  const handleSubmit = useCallback(async () => {
    // Validate form data - message is required, image is optional
    const validation = ValidationUtils.validateFormData(message, state.selectedImage);
    if (!validation.isValid) {
      onNotification(validation.error || VALIDATION_MESSAGES.PLEASE_CHECK_INPUT, 'error');
      return;
    }

    // For new submissions from home page, always start fresh conversation
    // This ensures each new message from home page starts a new conversation
    let conversationHistory: any[] = [];
    let conversationId: string | undefined;

    const formData: ChatFormData = {
      message: message.trim(),
      selectedImage: state.selectedImage,
      selectedFormat: state.selectedFormat,
      conversationId,
      assumptionMode: false, // Default to false since we removed the toggle
    };

    updateState({ isLoading: true });

    try {
      const apiResponse = await apiService.sendMessage(formData, conversationHistory);
      
      // Store the API response for use in ResponsePopup
      setLastApiResponse(apiResponse);
      
      // Add to chat history
      const conversation = addConversation(
        formData.message,
        apiResponse.response,
        apiResponse.imageAnalyzed || false,
        apiResponse.conversationId
      );
      
      // Open popup with response
      setPopupState({
        isOpen: true,
        response: apiResponse.response,
        imageAnalyzed: apiResponse.imageAnalyzed || false,
        conversationId: conversation.id, // Use our internal conversation ID
        followUpHistory: [],
        shouldShowEndPrompt: apiResponse.shouldShowEndPrompt || false,
        userResponseCount: apiResponse.userResponseCount || 0,
      });
      
      onNotification(MESSAGES.SUCCESS_RESPONSE_RECEIVED, 'success');
      
      // Clear the message input after successful submission
      setMessage('');
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : VALIDATION_MESSAGES.UNEXPECTED_ERROR;
      onNotification(errorMessage, 'error');
      
      const errorResponse = `❌ Error: ${errorMessage}\n\n${VALIDATION_MESSAGES.BACKEND_SERVER_ERROR}`;
      
      // Add error to chat history
      const conversation = addConversation(
        formData.message,
        errorResponse,
        false
      );
      
      // Show error in popup
      setPopupState({
        isOpen: true,
        response: errorResponse,
        imageAnalyzed: false,
        conversationId: conversation.id,
        followUpHistory: [],
        shouldShowEndPrompt: false,
        userResponseCount: 0,
      });
    } finally {
      updateState({ isLoading: false });
    }
  }, [message, state.selectedImage, state.selectedFormat, updateState, onNotification, addConversation, lastConversation]);

  const handleKeyDown = useCallback((event: React.KeyboardEvent) => {
    if (event.key === 'Enter' && (event.ctrlKey || event.metaKey)) {
      event.preventDefault();
      handleSubmit();
    }
  }, [handleSubmit]);

  const handleClosePopup = useCallback(() => {
    setPopupState(prev => ({ ...prev, isOpen: false }));
    setShowNewQuoteButton(false);
  }, []);

  const handleRequestNewQuote = useCallback(() => {
    setPopupState(prev => ({ ...prev, isOpen: false }));
    setShowNewQuoteButton(false);
    setMessage('');
    setLastApiResponse(null);
    onNotification('Ready for your next project! Enter your requirements below.', 'info');
  }, [onNotification]);

  const handleAskAgain = useCallback(() => {
    // Close the popup
    setPopupState(prev => ({ ...prev, isOpen: false }));
    // Clear the last API response and quote data
    setLastApiResponse(null);
    // Clear chat history to remove the last conversation
    clearHistory();
    // Clear the message input
    setMessage('');
    // Reset any other state
    setShowNewQuoteButton(false);
    // Show notification
    onNotification('Ready for a new question! Enter your project requirements below.', 'info');
  }, [clearHistory, onNotification]);

  const handleReopenLastResponse = useCallback(() => {
    if (lastConversation) {
      setPopupState({
        isOpen: true,
        response: lastConversation.originalResponse,
        imageAnalyzed: lastConversation.imageAnalyzed,
        conversationId: lastConversation.id,
        followUpHistory: lastConversation.followUpHistory,
        shouldShowEndPrompt: false, // Will be determined by conversation state
        userResponseCount: lastConversation.followUpHistory.length + 1,
      });
    }
  }, [lastConversation]);

  const handleFollowUpAdded = useCallback(async (conversationId: string, question: string, response: string) => {
    addFollowUp(conversationId, question, response);
    
    // Check if we should show end prompt after this follow-up
    // We need to get the updated conversation state from the backend
    try {
      const followUpData = { message: question, conversationId, assumptionMode: false };
      const apiResponse = await apiService.sendFollowUp(followUpData);
      
      // Update popup state with the latest shouldShowEndPrompt flag
      setPopupState(prev => ({
        ...prev,
        shouldShowEndPrompt: apiResponse.shouldShowEndPrompt || false,
        userResponseCount: apiResponse.userResponseCount || 0,
      }));
    } catch (error) {
      console.error('Error checking end prompt status:', error);
    }
  }, [addFollowUp]);

  return (
    <div className={`${layoutStyles.chatbotContainer} ${className || ''}`}>
      {/* Hide main card when quote is being shown, unless new quote button is active */}
      {(!(popupState.isOpen && popupState.shouldShowEndPrompt) || showNewQuoteButton) && (
        <div className={`${layoutStyles.mainCard} ${state.isLoading ? layoutStyles.loading : ''}`}>
          <h1 className={layoutStyles.title}>{MESSAGES.MAIN_TITLE}</h1>
          
          <p className={layoutStyles.description}>
            {MESSAGES.MAIN_DESCRIPTION}
          </p>
          
          <div className={layoutStyles.inputSection}>
            <div style={{ position: 'relative' }}>
              <textarea
                value={message}
                onChange={(e) => setMessage(e.target.value)}
                onKeyDown={handleKeyDown}
                className={`${formStyles.projectInput} ${
                  message.length > 675 ? 'error' : message.length > 600 ? 'warning' : ''
                }`}
                placeholder={PLACEHOLDERS.PROJECT_DESCRIPTION}
                rows={4}
                maxLength={750}
                disabled={state.isLoading}
                aria-label={LABELS.ARIA_PROJECT_DESCRIPTION}
              />
              <div className={`${layoutStyles.characterCounter} ${
                message.length > 675 ? 'error' : message.length > 600 ? 'warning' : ''
              }`}>
                {message.length}{LABELS.CHARACTER_COUNTER_SEPARATOR}750{LABELS.CHARACTERS_SUFFIX}
              </div>
            </div>
            
            <div className={layoutStyles.actionButtons}>
              <ImageUpload
                selectedImage={state.selectedImage}
                onImageChange={handleImageChange}
                disabled={state.isLoading}
              />
              
              <div className={buttonStyles.orText}>Or</div>
              
              <button
                type="button"
                className={buttonStyles.btnOutline}
                onClick={() => onNotification(MESSAGES.INFO_FIGMA_INTEGRATION, 'info')}
                disabled={state.isLoading}
              >
                <Palette size={16} style={{ marginRight: '6px' }} />
                {LABELS.LINK_FIGMA_DESIGN}
              </button>
              
              <FormSubmission
                onSubmit={handleSubmit}
                isLoading={state.isLoading}
                disabled={!message.trim() && !state.selectedImage}
              />
              
              {lastConversation && (
                <button
                  type="button"
                  className={buttonStyles.btnOutline}
                  onClick={handleReopenLastResponse}
                  disabled={state.isLoading}
                  style={{
                    marginLeft: '12px',
                    position: 'absolute',
                    top: '-20px',
                    right: '-10px',
                  }}
                >
                  <ClipboardList size={16} style={{ marginRight: '6px' }} />
                  {LABELS.REOPEN_LAST_RESPONSE}
                </button>
              )}
            </div>
          </div>
        </div>
      )}

      {/* {(!(popupState.isOpen && popupState.shouldShowEndPrompt) || showNewQuoteButton) && (
        <FormatSelector
          selectedFormat={state.selectedFormat}
          onFormatChange={handleFormatChange}
          disabled={state.isLoading}
        />
      )} */}
      
      <ResponsePopup
        popupState={popupState}
        onClose={handleClosePopup}
        onNotification={onNotification}
        onFollowUpAdded={handleFollowUpAdded}
        projectDescription={lastConversation?.originalMessage || message}
        projectFormat={state.selectedFormat}
        assumptionMode={false}
        autoQuoteData={lastApiResponse?.quoteData || lastConversation?.quoteData}
        onRequestNewQuote={handleRequestNewQuote}
        onQuoteGenerated={(quote) => {
          if (popupState.conversationId) {
            updateQuoteData(popupState.conversationId, quote);
          }
          onNotification('Quote generated successfully!', 'success');
        }}
        onAskAgain={handleAskAgain}
      />
    </div>

  );
};

export { ChatbotContainer };
export default ChatbotContainer;
