import React from 'react';
import { LABELS } from '@/constants';
import buttonStyles from '@/styles/buttons.module.scss';

interface FormSubmissionProps {
  onSubmit: () => void;
  isLoading: boolean;
  disabled?: boolean;
  className?: string;
}

const FormSubmission: React.FC<FormSubmissionProps> = ({
  onSubmit,
  isLoading,
  disabled = false,
  className,
}) => {
  const handleClick = () => {
    if (!disabled && !isLoading) {
      onSubmit();
    }
  };

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' || e.key === ' ') {
      e.preventDefault();
      handleClick();
    }
  };

  return (
    <button
      type="button"
      className={`${buttonStyles.btnPrimary} ${isLoading ? buttonStyles.btnLoading : ''} ${className || ''}`}
      onClick={handleClick}
      onKeyDown={handleKeyDown}
      disabled={disabled || isLoading}
      aria-label={isLoading ? LABELS.ARIA_SUBMIT_LOADING : LABELS.ARIA_SUBMIT_DEFAULT}
    >
      {isLoading ? (
        <>
          <span style={{ visibility: 'hidden' }}>{LABELS.SUBMIT}</span>
          <span className={buttonStyles.spinner} aria-hidden="true">{LABELS.LOADING_SPINNER_EMOJI}</span>
        </>
      ) : (
        LABELS.SUBMIT
      )}
    </button>
  );
};

export { FormSubmission };
export default FormSubmission;
