import React from 'react';
import { Phone, Calendar, MessageCircle } from 'lucide-react';
import popupStyles from '@/styles/popup.module.scss';

interface RotatingCTAProps {
  userResponseCount: number;
  onNotification: (message: string, type: 'info' | 'success' | 'warning' | 'error') => void;
}

const RotatingCTA: React.FC<RotatingCTAProps> = ({ userResponseCount, onNotification }) => {
  // Determine which CTA to show based on userResponseCount
  // We want: 1st response = CTA #1, 2nd response = CTA #2, 3rd response = CTA #3, 4th response = CTA #1, etc.
  // So we use (userResponseCount - 1) % 3 to get 0-indexed array position
  const ctaIndex = ((userResponseCount - 1) % 3 + 3) % 3; // Handle negative numbers gracefully

  const handleCallClick = () => {
    // Create a clickable phone link
    window.location.href = 'tel:************';
    onNotification('Calling ************...', 'info');
  };

  const handleScheduleClick = () => {
    // For now, show notification - can be replaced with actual scheduling link
    onNotification('Contact us to schedule your whiteboarding session!', 'info');
  };

  const handleContactClick = () => {
    // For now, show notification - can be replaced with actual contact form
    onNotification('Contact us to vibe code your first version and get clear next steps!', 'info');
  };

  const ctas = [
    {
      id: 1,
      text: "Have questions about the quote and what it looks like in detail?",
      buttonText: "Call us",
      buttonAction: handleCallClick,
      icon: Phone,
      phoneNumber: "************"
    },
    {
      id: 2,
      text: "Want to scope the product out so you can see a first version of it?",
      buttonText: "Schedule a whiteboarding session",
      buttonAction: handleScheduleClick,
      icon: Calendar
    },
    {
      id: 3,
      text: "Ready to get started?",
      buttonText: "Contact us to vibe code your first version and get clear next steps",
      buttonAction: handleContactClick,
      icon: MessageCircle
    }
  ];

  const currentCTA = ctas[ctaIndex];

  return (
    <div className={popupStyles.rotatingCtaSection}>
      <div className={popupStyles.rotatingCtaCard}>
        <div className={popupStyles.rotatingCtaContent}>
          <currentCTA.icon size={20} className={popupStyles.rotatingCtaIcon} />
          <p className={popupStyles.rotatingCtaText}>
            {currentCTA.text}
          </p>
          <button
            className={popupStyles.rotatingCtaButton}
            onClick={currentCTA.buttonAction}
            type="button"
          >
            {currentCTA.buttonText}
            {/* {currentCTA.phoneNumber && (
              <span className={popupStyles.rotatingCtaPhone}>
                {currentCTA.phoneNumber}
              </span>
            )} */}
          </button>
        </div>
      </div>
    </div>
  );
};

export { RotatingCTA };
export default RotatingCTA;
