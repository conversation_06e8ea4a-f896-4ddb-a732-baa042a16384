import React from 'react';
import { RotateCcw, Loader2 } from 'lucide-react';
import baseStyles from '@/styles/base.module.scss';

interface BackgroundRefreshProps {
  onRefresh: () => void;
  isLoading: boolean;
  className?: string;
}

const BackgroundRefresh: React.FC<BackgroundRefreshProps> = ({
  onRefresh,
  isLoading,
  className,
}) => {
  const handleClick = () => {
    if (!isLoading) {
      onRefresh();
    }
  };

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if ((e.key === 'Enter' || e.key === ' ') && !isLoading) {
      e.preventDefault();
      onRefresh();
    }
  };

  return (
    <button
      type="button"
      className={`${baseStyles.backgroundRefresh} ${className || ''}`}
      onClick={handleClick}
      onKeyDown={handleKeyDown}
      disabled={isLoading}
      aria-label={isLoading ? 'Loading new background...' : 'Refresh background image'}
      title={isLoading ? 'Loading new background...' : 'Refresh background image (Ctrl+R)'}
    >
      {isLoading ? (
        <Loader2 size={20} className={baseStyles.spinner} aria-hidden="true" style={{ animation: 'spin 1s linear infinite' }} />
      ) : (
        <RotateCcw size={20} />
      )}
    </button>
  );
};

export { BackgroundRefresh };
export default BackgroundRefresh;
