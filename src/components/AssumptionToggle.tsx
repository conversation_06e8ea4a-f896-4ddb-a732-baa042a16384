import React from 'react';
import { HelpCircle, Lightbulb } from 'lucide-react';
import buttonStyles from '@/styles/buttons.module.scss';
import layoutStyles from '@/styles/layout.module.scss';

interface AssumptionToggleProps {
  assumptionMode: boolean;
  onToggle: (mode: boolean) => void;
  disabled?: boolean;
  className?: string;
}

const AssumptionToggle: React.FC<AssumptionToggleProps> = ({
  assumptionMode,
  onToggle,
  disabled = false,
  className = '',
}) => {
  return (
    <div className={`${layoutStyles.assumptionToggle} ${className}`}>
      <div className={layoutStyles.toggleLabel}>
        Response Style:
      </div>
      <div className={layoutStyles.toggleButtons}>
        <button
          type="button"
          className={`${buttonStyles.toggleButton} ${!assumptionMode ? buttonStyles.active : ''}`}
          onClick={() => onToggle(false)}
          disabled={disabled}
          aria-label="Ask Questions Mode"
          title="AI will ask specific questions to understand your project"
        >
          <HelpCircle size={16} style={{ marginRight: '6px' }} />
          Ask Questions
        </button>
        <button
          type="button"
          className={`${buttonStyles.toggleButton} ${assumptionMode ? buttonStyles.active : ''}`}
          onClick={() => onToggle(true)}
          disabled={disabled}
          aria-label="Make Assumptions Mode"
          title="AI will make reasonable assumptions about your project"
        >
          <Lightbulb size={16} style={{ marginRight: '6px' }} />
          Make Assumptions
        </button>
      </div>
      <div className={layoutStyles.toggleDescription}>
        {assumptionMode 
          ? "AI will make educated assumptions about your project based on common patterns"
          : "AI will ask specific questions to understand your project requirements"
        }
      </div>
    </div>
  );
};

export { AssumptionToggle };
export default AssumptionToggle;
