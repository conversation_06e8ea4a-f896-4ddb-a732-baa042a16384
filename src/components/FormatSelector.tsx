import React, { useState, useRef, useEffect, useCallback } from 'react';
import { PROJECT_FORMATS } from '@/types';
import { MESSAGES, LABELS } from '@/constants';
import type { FormatSelectorProps } from '@/types';
import layoutStyles from '@/styles/layout.module.scss';
import buttonStyles from '@/styles/buttons.module.scss';
import formStyles from '@/styles/forms.module.scss';

const FormatSelector: React.FC<FormatSelectorProps> = ({
  selectedFormat,
  onFormatChange,
  disabled = false,
  className,
}) => {
  const [dropdownOpen, setDropdownOpen] = useState(false);
  const dropdownRef = useRef<HTMLDivElement>(null);

  const basicFormats = PROJECT_FORMATS.slice(0, 2); // Web, Webapp
  const otherFormats = PROJECT_FORMATS.slice(2); // All others

  const selectedFormatLabel = PROJECT_FORMATS.find(f => f.value === selectedFormat)?.label || LABELS.OTHER;
  const isOtherSelected = !basicFormats.some(f => f.value === selectedFormat);

  const handleBasicFormatClick = useCallback((format: string) => {
    if (disabled) return;
    onFormatChange(format);
    setDropdownOpen(false);
  }, [disabled, onFormatChange]);

  const handleOtherFormatClick = useCallback((format: string) => {
    if (disabled) return;
    onFormatChange(format);
    setDropdownOpen(false);
  }, [disabled, onFormatChange]);

  const toggleDropdown = useCallback((e: React.MouseEvent) => {
    if (disabled) return;
    e.stopPropagation();
    setDropdownOpen(prev => !prev);
  }, [disabled]);

  const handleKeyDown = useCallback((e: React.KeyboardEvent) => {
    if (disabled) return;
    
    if (e.key === 'Enter' || e.key === ' ') {
      e.preventDefault();
      setDropdownOpen(prev => !prev);
    } else if (e.key === 'Escape') {
      setDropdownOpen(false);
    }
  }, [disabled]);

  // Close dropdown when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {
        setDropdownOpen(false);
      }
    };

    if (dropdownOpen) {
      document.addEventListener('click', handleClickOutside);
      return () => document.removeEventListener('click', handleClickOutside);
    }
  }, [dropdownOpen]);

  return (
    <div className={`${layoutStyles.formatCard} ${className || ''}`}>
      <div className={layoutStyles.formatQuestion}>
        <span>{MESSAGES.FORMAT_QUESTION}</span>
        <div className={layoutStyles.formatOptions}>
          {basicFormats.map((format) => (
            <button
              key={format.id}
              type="button"
              className={`${buttonStyles.btnFormat} ${
                selectedFormat === format.value ? buttonStyles.active : ''
              }`}
              onClick={() => handleBasicFormatClick(format.value)}
              disabled={disabled}
              aria-pressed={selectedFormat === format.value}
            >
              {format.label}
            </button>
          ))}
          
          <div className={formStyles.dropdown} ref={dropdownRef}>
            <button
              type="button"
              className={`${buttonStyles.btnFormat} ${buttonStyles.dropdownBtn} ${
                isOtherSelected ? buttonStyles.active : ''
              }`}
              onClick={toggleDropdown}
              onKeyDown={handleKeyDown}
              disabled={disabled}
              aria-expanded={dropdownOpen}
              aria-haspopup="listbox"
              aria-label={LABELS.ARIA_OTHER_FORMAT_OPTIONS}
            >
              {isOtherSelected ? selectedFormatLabel : LABELS.OTHER}{' '}
              <span className={buttonStyles.dropdownArrow}>{LABELS.DROPDOWN_ARROW}</span>
            </button>
            
            <div
              className={`${formStyles.dropdownMenu} ${dropdownOpen ? formStyles.show : ''}`}
              role="listbox"
              aria-label={LABELS.ARIA_FORMAT_OPTIONS}
            >
              {otherFormats.map((format) => (
                <div
                  key={format.id}
                  className={`${formStyles.dropdownItem} ${
                    selectedFormat === format.value ? formStyles.selected : ''
                  }`}
                  onClick={() => handleOtherFormatClick(format.value)}
                  role="option"
                  aria-selected={selectedFormat === format.value}
                  tabIndex={0}
                  onKeyDown={(e) => {
                    if (e.key === 'Enter' || e.key === ' ') {
                      e.preventDefault();
                      handleOtherFormatClick(format.value);
                    }
                  }}
                >
                  {format.label}
                </div>
              ))}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export { FormatSelector };
export default FormatSelector;
