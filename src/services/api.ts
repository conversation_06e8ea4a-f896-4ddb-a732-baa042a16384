import type { ApiR<PERSON>ponse, FormData, FollowUpData } from '@/types';

const API_BASE_URL = 'https://5dacn8xif1.execute-api.us-east-1.amazonaws.com/prod/api';

export class ApiService {
  private static instance: ApiService;

  public static getInstance(): ApiService {
    if (!ApiService.instance) {
      ApiService.instance = new ApiService();
    }
    return ApiService.instance;
  }

  private constructor() {}

  async sendMessage(formData: FormData, conversationHistory?: any[]): Promise<ApiResponse> {
    console.log('API: Starting sendMessage request');
    console.log('API: Form data:', {
      messageLength: formData.message.length,
      hasImage: !!formData.selectedImage,
      format: formData.selectedFormat,
      imageSize: formData.selectedImage ? `${(formData.selectedImage.size / 1024).toFixed(2)}KB` : 'N/A',
      historyLength: conversationHistory?.length || 0
    });

    try {
      let response: Response;

      if (formData.selectedImage) {
        console.log('API: Handling image upload request');
        // Handle image upload
        const uploadData = new FormData();
        uploadData.append('image', formData.selectedImage);
        uploadData.append('message', formData.message);
        uploadData.append('format', formData.selectedFormat);

        console.log('API: Sending POST request to /upload endpoint');
        response = await fetch(`${API_BASE_URL}/upload`, {
          method: 'POST',
          body: uploadData,
        });
      } else {
        console.log('API: Handling text-only chat request');
        // Build conversation messages including history
        const messages = [
          ...(conversationHistory || []),
          { role: 'user', content: formData.message }
        ];
        
        const requestBody = {
          messages,
          format: formData.selectedFormat,
          conversationId: formData.conversationId,
          assumptionMode: formData.assumptionMode
        };
        console.log('API: Sending POST request to /chat endpoint with body:', {
          ...requestBody,
          messages: `${messages.length} messages`
        });
        
        response = await fetch(`${API_BASE_URL}/chat`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify(requestBody),
        });
      }

      console.log('API: Response received:', {
        status: response.status,
        statusText: response.statusText,
        ok: response.ok
      });

      if (!response.ok) {
        console.error('❌ API: Response not OK, attempting to parse error');
        const errorData = await response.json().catch(() => ({}));
        console.error('❌ API: Error data:', errorData);
        throw new Error(errorData.error || `HTTP error! status: ${response.status}`);
      }

      const data = await response.json();
      console.log('✅ API: Success response data:', {
        responseLength: data.response?.length || 0,
        imageAnalyzed: data.imageAnalyzed,
        conversationId: data.conversationId
      });

      const result = {
        response: data.response,
        imageAnalyzed: data.imageAnalyzed || false,
        conversationId: data.conversationId || this.generateConversationId(),
        shouldShowEndPrompt: data.shouldShowEndPrompt || false,
        userResponseCount: data.userResponseCount || 0,
      };

      console.log('API: sendMessage completed successfully');
      return result;
    } catch (error) {
      console.error('API: sendMessage error occurred:', error);
      console.error('API: Error details:', {
        name: error instanceof Error ? error.name : 'Unknown',
        message: error instanceof Error ? error.message : 'Unknown error',
        stack: error instanceof Error ? error.stack : 'No stack trace'
      });
      
      if (error instanceof Error) {
        throw new Error(error.message);
      }
      
      throw new Error('An unexpected error occurred');
    }
  }

  async sendFollowUp(followUpData: FollowUpData, conversationHistory?: any[]): Promise<ApiResponse> {
    console.log('API: Starting sendFollowUp request');
    console.log('API: Follow-up data:', {
      messageLength: followUpData.message.length,
      conversationId: followUpData.conversationId,
      historyLength: conversationHistory?.length || 0
    });

    try {
      // Build conversation messages including history
      const messages = [
        ...(conversationHistory || []),
        { role: 'user', content: followUpData.message }
      ];
      
      const requestBody = {
        messages,
        conversationId: followUpData.conversationId,
        assumptionMode: followUpData.assumptionMode,
      };
      console.log('API: Sending POST request to /followup endpoint with body:', {
        ...requestBody,
        messages: `${messages.length} messages`
      });

      const response = await fetch(`${API_BASE_URL}/followup`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(requestBody),
      });

      console.log('API: Follow-up response received:', {
        status: response.status,
        statusText: response.statusText,
        ok: response.ok
      });

      if (!response.ok) {
        console.error('❌ API: Follow-up response not OK, attempting to parse error');
        const errorData = await response.json().catch(() => ({}));
        console.error('❌ API: Follow-up error data:', errorData);
        throw new Error(errorData.error || `HTTP error! status: ${response.status}`);
      }

      const data = await response.json();
      console.log('✅ API: Follow-up success response data:', {
        responseLength: data.response?.length || 0,
        conversationId: data.conversationId
      });

      const result = {
        response: data.response,
        imageAnalyzed: false,
        conversationId: data.conversationId || followUpData.conversationId,
        shouldShowEndPrompt: data.shouldShowEndPrompt || false,
        userResponseCount: data.userResponseCount || 0,
      };

      console.log('API: sendFollowUp completed successfully');
      return result;
    } catch (error) {
      console.error('API: sendFollowUp error occurred:', error);
      console.error('API: Follow-up error details:', {
        name: error instanceof Error ? error.name : 'Unknown',
        message: error instanceof Error ? error.message : 'Unknown error',
        stack: error instanceof Error ? error.stack : 'No stack trace'
      });
      
      if (error instanceof Error) {
        throw new Error(error.message);
      }
      
      throw new Error('An unexpected error occurred');
    }
  }

  private generateConversationId(): string {
    const id = `conv_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    console.log('🆔 API: Generated new conversation ID:', id);
    return id;
  }

  async healthCheck(): Promise<boolean> {
    console.log('API: Starting health check');
    
    try {
      console.log('API: Sending GET request to /health endpoint');
      const response = await fetch(`${API_BASE_URL}/health`, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
        },
      });
      
      console.log('API: Health check response:', {
        status: response.status,
        statusText: response.statusText,
        ok: response.ok
      });
      
      const isHealthy = response.ok;
      console.log(`${isHealthy ? '✅' : '❌'} API: Health check result: ${isHealthy ? 'HEALTHY' : 'UNHEALTHY'}`);
      
      return isHealthy;
    } catch (error) {
      console.warn('API: Health check failed with error:', error);
      console.warn('API: Health check error details:', {
        name: error instanceof Error ? error.name : 'Unknown',
        message: error instanceof Error ? error.message : 'Unknown error'
      });
      return false;
    }
  }

  isOnline(): boolean {
    const online = navigator.onLine;
    console.log(`API: Network status check - ${online ? 'ONLINE' : 'OFFLINE'}`);
    return online;
  }
}

// Export singleton instance
export const apiService = ApiService.getInstance();
export default apiService;
