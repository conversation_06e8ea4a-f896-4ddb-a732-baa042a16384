import { useState, useCallback } from 'react';
import type { ChatbotState, UseStateManager } from '@/types';

const initialState: ChatbotState = {
  selectedFormat: 'web',
  selectedImage: null,
  isLoading: false,
  dropdownOpen: false,
  assumptionMode: false,
};

export const useStateManager = (): UseStateManager => {
  const [state, setState] = useState<ChatbotState>(initialState);

  const updateState = useCallback((updates: Partial<ChatbotState>) => {
    setState(prevState => ({ ...prevState, ...updates }));
  }, []);

  const resetState = useCallback(() => {
    setState(initialState);
  }, []);

  return {
    state,
    updateState,
    resetState,
  };
};

export default useStateManager;
