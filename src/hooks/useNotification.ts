import { useState, useCallback } from 'react';
import type { NotificationProps } from '@/types';

interface NotificationState extends Omit<NotificationProps, 'onClose'> {
  id: string;
  visible: boolean;
}

export const useNotification = () => {
  const [notifications, setNotifications] = useState<NotificationState[]>([]);

  const showNotification = useCallback((
    message: string,
    type: NotificationProps['type'] = 'info',
    duration: number = 4000
  ) => {
    const id = Date.now().toString();
    const notification: NotificationState = {
      id,
      message,
      type,
      duration,
      visible: true,
    };

    setNotifications(prev => [...prev, notification]);

    // Auto-remove after duration
    setTimeout(() => {
      setNotifications(prev => 
        prev.map(n => n.id === id ? { ...n, visible: false } : n)
      );
      
      // Remove from array after animation
      setTimeout(() => {
        setNotifications(prev => prev.filter(n => n.id !== id));
      }, 300);
    }, duration);

    return id;
  }, []);

  const hideNotification = useCallback((id: string) => {
    setNotifications(prev => 
      prev.map(n => n.id === id ? { ...n, visible: false } : n)
    );
    
    setTimeout(() => {
      setNotifications(prev => prev.filter(n => n.id !== id));
    }, 300);
  }, []);

  const clearAllNotifications = useCallback(() => {
    setNotifications([]);
  }, []);

  return {
    notifications,
    showNotification,
    hideNotification,
    clearAllNotifications,
  };
};

export default useNotification;
