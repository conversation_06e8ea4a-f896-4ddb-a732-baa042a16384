import React, { useState, useEffect, useCallback } from 'react';
import baseStyles from '@/styles/base.module.scss';

// Static image and credit info
const STATIC_IMAGES = [
  {
    url: 'https://images.unsplash.com/photo-1751420195738-239002789547?q=80&w=1528&auto=format&fit=crop&ixlib=rb-4.1.0&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D',
    pageUrl: 'https://unsplash.com/photos/glowing-portal-appears-over-a-serene-sunset-landscape-uduYUqjJkDw',
    author: 'itsiken',
    authorProfile: 'https://unsplash.com/@itsiken',
    authorAvatar: 'https://images.unsplash.com/profile-1737858023382-49538fb37075image?w=64&dpr=2&crop=faces&bg=%23fff&h=64&auto=format&fit=crop&q=60&ixlib=rb-4.1.0',
  },
];

export const useUnsplashBackground = () => {
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [attributionText, setAttributionText] = useState<string | null>(null);

  const setBackgroundImage = useCallback(async () => {
  //   setIsLoading(true);
  //   setError(null);
  //   // Always set gradient as initial background
  //   document.body.style.background = 'linear-gradient(135deg, #0ea5e9 0%, #3a86ff 100%)';
    document.body.style.background = 'black';
  //   console.log('[BG] Set gradient fallback:', document.body.style.background);
  //   try {
  //     // Only one image for now
  //     const image = STATIC_IMAGES[0];
  //     const img = new window.Image();
  //     console.log('[BG] Start loading image:', image.url);
  //     await new Promise<void>((resolve, reject) => {
  //       img.onload = () => {
  //         document.body.style.backgroundImage = `url(${image.url})`;
  //         document.body.style.backgroundSize = 'cover';
  //         document.body.style.backgroundPosition = 'center';
  //         document.body.style.backgroundRepeat = 'no-repeat';
  //         document.body.style.backgroundAttachment = 'fixed';
  //         // Add overlay for better text readability
  //         if (!document.querySelector('.backgroundOverlay')) {
  //           // const overlay = document.createElement('div');
  //           // overlay.className = 'backgroundOverlay';
  //           // document.body.appendChild(overlay);
  //         }
  //         resolve();
  //       };
  //       img.onerror = () => {
  //         console.log('[BG] Image failed to load:', image.url);
  //         // Keep the gradient fallback
  //         setError('Failed to load background image');
  //         reject(new Error('Failed to load background image'));
  //       };
  //       // img.src = image.url;
  //     });
  //   } catch (err) {
  //     // Gradient is already set
  //     setError('Failed to load background image');
  //     console.log('[BG] Using gradient fallback due to error:', err);
  //   } finally {
  //     setIsLoading(false);
  //   }
  }, []);

  const refreshBackground = useCallback(() => {
    setBackgroundImage();
  }, [setBackgroundImage]);

  useEffect(() => {
    setAttributionText('default');
    setBackgroundImage();
  }, [setBackgroundImage]);

  // Attribution element (JSX)
  const image = STATIC_IMAGES[0];
  const attributionElement = React.useMemo(() => {
    return null;
    // if (!attributionText) return null;
    // return (
    //   <div className={baseStyles.photoAttribution}>
    //     <a href={image.authorProfile} target="_blank" rel="noopener noreferrer">
    //       <img src={image.authorAvatar} alt={image.author} style={{ borderRadius: '50%', width: 24, height: 24, verticalAlign: 'middle', marginRight: 8 }} />
    //     </a>
    //     <span>
    //       Background Photo by{' '}
    //       <a href={image.authorProfile} target="_blank" rel="noopener noreferrer">{image.author}</a>
    //     </span>
    //   </div>
    // );
  }, [attributionText, image]);

  return {
    refreshBackground,
    isLoading,
    error,
    attributionElement,
  };
};

export default useUnsplashBackground;
