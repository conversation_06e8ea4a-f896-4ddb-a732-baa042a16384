// Validation error messages
export const VALIDATION_MESSAGES = {
  // General validation
  PLEASE_CHECK_INPUT: "Please check your input",
  
  // Message validation
  DESCRIBE_PROJECT: "Please describe your project",
  MESSAGE_TOO_SHORT: "Message must be at least {min} characters long",
  MESSAGE_TOO_LONG: "Message must be less than {max} characters",
  
  // File validation
  NO_FILE_SELECTED: "No file selected",
  INVALID_IMAGE_FILE: "Please select a valid image file (JPEG, PNG, GIF, or WebP)",
  FILE_TOO_LARGE: "File size must be less than {maxSize}MB",
  INVALID_FILE: "Invalid file",
  
  // Format validation
  SELECT_PROJECT_FORMAT: "Please select a project format",
  
  // Email validation
  EMAIL_REQUIRED: "Email is required",
  INVALID_EMAIL: "Please enter a valid email address",
  
  // URL validation
  URL_REQUIRED: "URL is required",
  INVALID_URL: "Please enter a valid URL",
  
  // API errors
  UNEXPECTED_ERROR: "An unexpected error occurred",
  BACKEND_SERVER_ERROR: "Please make sure the backend server is running and try again.",
  FOLLOWUP_SEND_FAILED: "Failed to send follow-up",
} as const;
