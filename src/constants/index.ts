// Export all constants from a single entry point
export { MESSAGES } from './messages';
export { <PERSON>BELS } from './labels';
export { PLACEHOLDERS } from './placeholders';
export { VALIDATION_MESSAGES } from './validation';

// Utility function to replace placeholders in validation messages
export const formatValidationMessage = (message: string, replacements: Record<string, string | number>): string => {
  let formattedMessage = message;
  Object.entries(replacements).forEach(([key, value]) => {
    formattedMessage = formattedMessage.replace(`{${key}}`, String(value));
  });
  return formattedMessage;
};
