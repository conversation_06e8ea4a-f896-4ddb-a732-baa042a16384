# Constants Directory

This directory contains all centralized constants used throughout the application. All inline text content, labels, placeholders, and messages have been extracted here for better maintainability and easier localization.

## File Structure

### `messages.ts`
Contains user-facing messages, descriptions, and prompts:
- Main interface messages (titles, descriptions)
- Format selector questions
- Response popup titles
- Notification messages
- Follow-up section prefixes

### `labels.ts`
Contains button labels, titles, and UI text elements:
- Button text (Submit, Send, Attach Image, etc.)
- ARIA labels for accessibility
- Image preview text
- Character counter formatting
- Loading states and icons

### `placeholders.ts`
Contains input placeholders and example text:
- Main textarea placeholder with multi-line examples
- Follow-up question placeholder with keyboard shortcuts

### `validation.ts`
Contains validation error messages:
- Form validation errors
- File upload errors
- API error messages
- Email and URL validation messages

### `index.ts`
Central export file that:
- Exports all constants from other files
- Provides utility functions like `formatValidationMessage()`
- Serves as single import point for components

## Usage Examples

### Importing Constants
```typescript
// Import specific constant groups
import { MESSAGES, LA<PERSON>LS } from '@/constants';

// Import individual groups
import { VALIDATION_MESSAGES } from '@/constants/validation';
```

### Using Constants in Components
```typescript
// Instead of inline strings
<h1>What do you want to build today?</h1>

// Use constants
<h1>{MESSAGES.MAIN_TITLE}</h1>
```

### Validation Messages with Placeholders
```typescript
// For dynamic messages with placeholders
import { formatValidationMessage } from '@/constants';

const error = formatValidationMessage(
  VALIDATION_MESSAGES.MESSAGE_TOO_LONG, 
  { max: 750 }
);
```

## Benefits

1. **Maintainability**: All text content in one place
2. **Consistency**: Ensures consistent messaging across the app
3. **Localization Ready**: Easy to implement i18n in the future
4. **Type Safety**: All constants are properly typed
5. **Searchability**: Easy to find and update specific text
6. **Reusability**: Constants can be shared across components

## Migration Notes

All components have been updated to use these constants instead of inline strings:
- `ChatbotContainer.tsx` - Main interface text
- `FormatSelector.tsx` - Format selection labels
- `ImageUpload.tsx` - Upload functionality text
- `FormSubmission.tsx` - Submit button states
- `ResponsePopup.tsx` - Popup interface text
- `ValidationUtils.ts` - Error messages

## Future Enhancements

This structure makes it easy to:
- Add internationalization (i18n) support
- Implement theme-based messaging
- Add A/B testing for different text variations
- Maintain consistent tone and voice across the application
