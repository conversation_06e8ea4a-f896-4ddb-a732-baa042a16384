import React from 'react';
import ReactDOM from 'react-dom/client';
import App from './App';
import baseStyles from '@/styles/base.module.scss';

// Apply base styles to body
document.body.className = baseStyles.body;

// Add global styles for CSS reset
const style = document.createElement('style');
style.textContent = `
  *, *::before, *::after {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
  }
`;
document.head.appendChild(style);

// Add keyboard shortcuts
document.addEventListener('keydown', (e) => {
  // Ctrl/Cmd + R to refresh background
  if ((e.ctrlKey || e.metaKey) && e.key === 'r') {
    e.preventDefault();
    const refreshBtn = document.querySelector('[aria-label*="Refresh background"]') as HTMLButtonElement;
    if (refreshBtn && !refreshBtn.disabled) {
      refreshBtn.click();
    }
  }
  
  // Escape to close dropdowns/modals
  if (e.key === 'Escape') {
    const dropdowns = document.querySelectorAll('[aria-expanded="true"]');
    dropdowns.forEach(dropdown => {
      if (dropdown instanceof HTMLElement) {
        dropdown.click();
      }
    });
  }
});

// Initialize React app
const root = ReactDOM.createRoot(
  document.getElementById('root') as HTMLElement
);

root.render(
  <React.StrictMode>
    <App />
  </React.StrictMode>
);
